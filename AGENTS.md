# AGENTS.md - Coding Guidelines for AI Agents

## Build, Lint, and Test Commands
- Run all tests: `uv run pytest tests/`
- Run a single test: `uv run pytest tests/test_module.py::test_function`
- Run a single test file: `uv run pytest tests/unit/graph/v4/nodes/test_suggest_career_path.py -v`
- Run with coverage: `uv run pytest tests/ --cov=app.graph.v4 --cov=app.api.routers.v4 --cov-report=html`
- Lint & format: `uv run ruff check && uv run ruff format`
- Start dev server: `uv run python main.py`

## Pre-commit Hooks
- Enforced by `.pre-commit-config.yaml`:
  - Ruff lint & format (`ruff`, `ruff-format`)
  - Bandit (high severity)
  - Gitleaks for secrets
  - End-of-file, trailing whitespace, merge/case conflict checks

## Code Style Guidelines
- **Imports**: Group stdlib, third-party, then local (absolute, e.g., `from app.`)
- **Functions**: Use `snake_case`, prefix with `async` for async functions
- **Types**: Use Pydantic models for validation, type hints everywhere
- **Error Handling**: Use try/except with specific exceptions, log errors
- **Testing**: Write async tests with `pytest-asyncio`, use fixtures from `conftest.py`
- **Formatting**: Ruff enforces 120-char lines, double quotes
- **Naming**: Use descriptive names (e.g., `evaluate_career_paths_parallel`)
- **State Management**: Use typed state classes (InputState, AgentState, OutputState)
- **Services**: Access via getter functions (e.g., `get_neo4j_service()`)
- **Versioning**: New features go in latest version (v4), maintain backward compatibility
