# Upzi Career Path – v4 Architecture

_Last updated: August 13, 2025_

## Overview

This document describes the architecture and workflow of the Upzi Career Path system, focusing on version 4 (v4). It is intended for developers integrating, extending, or maintaining the system.

v4 introduces a LangGraph-based workflow for career path generation, leveraging advanced LLM chains, graph databases, and a robust service layer.

---

## High-Level Architecture Diagram

```mermaid
flowchart TD
    Start([Start])
    SuggestOrGenerate{career_path provided?}
    Suggest[Suggest Career Path Node]
    Get[Get Career Path Node]
    Postprocess[Postprocess Career Path Node]
    End([End])

    Start --> SuggestOrGenerate
    SuggestOrGenerate -- No --> Suggest
    SuggestOrGenerate -- Yes --> Get
    Suggest --> Get
    Get --> Postprocess
    Postprocess --> End
```

**Workflow Summary:**
- Entry point checks if a career path is provided.
- If not, the system suggests career paths.
- The selected or provided career path is analyzed.
- Results are post-processed for output.

---

## Core Components

### API v4 Structure

- **Entry Point:** `/api/v4/career_path/api.py`
- **Graph Workflow:** Defined in `app/graph/v4/graph.py`
- **State Management:** Typed state classes in `app/graph/v4/state.py`

### Graph Nodes

- **suggest_career_path:**
  - Suggests career paths based on user profile and group function analysis.
  - Uses LLM chains and Neo4j queries for recommendations.

- **get_career_path:**
  - Evaluates and analyzes a specific career path.
  - Calculates matching scores, retrieves job info, and skills from Neo4j.

- **postprocess_career_path:**
  - Formats the final output, including salary data, job progressions, and statistics.
  - Uses cache and salary utilities for enriched results.

### Chains & Prompts

- Specialized LLM chains for:
  - Group function suggestion
  - Career path evaluation
  - Output formatting
- Prompts are structured for consistent LLM responses.

### Models & Responses

- Typed models for input, agent, and output state.
- Structured responses for career path details, skills, salary, and statistics.

### State Management

- **InputState:** User profile and request parameters.
- **AgentState:** Intermediate data, career path candidates, and evaluation results.
- **OutputState:** Final response payload.

---

## Database Stack

- **Neo4j:**
  - Graph database for career relationships, job progressions, and skills.
  - Used for complex queries and batch lookups.

- **MongoDB:**
  - Document store for user profiles and secondary cache.

- **Redis:**
  - Primary caching layer for performance optimization.

**Data Flow:**
- User input triggers LLM and database queries.
- Results are cached for efficiency.
- Neo4j is the source of truth for career path and job data.

---

## Service Layer

- **LLM Service:**
  - Optimized ChatOpenAI instances with connection pooling.
  - Used for all prompt-based analysis and suggestions.

- **Database Services:**
  - Managed connections for Neo4j, MongoDB, and Redis.

- **Cache Layer:**
  - Dual caching strategy (Redis primary, MongoDB secondary).
  - Deterministic cache keys for repeatable results.

---

## Application Lifecycle

- **Startup:**
  - Initializes database connections and logging.

- **Shutdown:**
  - Cleans up HTTP clients and database connections.

- **Monitoring:**
  - Sentry for error tracking.
  - Langfuse for LLM observability.

---

## Testing Framework

- **Unit Tests:**
  - Located in `tests/unit/`
  - Covers API, graph nodes, and utility functions.

- **Integration Tests:**
  - Located in `tests/integration/`
  - Simulates end-to-end workflows.

- **Fixtures & Mocks:**
  - Used for user input, career data, and service layers.

---

## Extensibility & Maintenance

- **Adding New Nodes:**
  - Implement in `app/graph/v4/nodes/`
  - Register in `graph.py` and update workflow edges.

- **Extending Chains/Prompts:**
  - Add new chains in `app/graph/v4/chains/`
  - Create prompts in `app/graph/v4/prompts/`

- **Service Layer:**
  - Add new services in `app/services/`
  - Ensure proper connection management.

---

## Troubleshooting & Best Practices

- **Cache Misses:**
  - Check Redis and MongoDB connectivity.
  - Validate cache key generation.

- **Neo4j Query Issues:**
  - Ensure career path and group function exist.
  - Use batched queries for performance.

- **LLM Errors:**
  - Validate prompt formatting and model selection.

- **Testing:**
  - Use provided fixtures and mocks for reliable tests.

---

## References & Related Docs

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [Neo4j Python Driver](https://neo4j.com/docs/api/python-driver/current/)
- [OpenAI API](https://platform.openai.com/docs/api-reference)
- See main `README.md` for setup and development commands.

---
