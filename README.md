# Upzi Career Path

A Python-based FastAPI application that provides AI-powered career path suggestions and recommendations using LangGraph for complex AI workflow orchestration.

## Features

- Multi-version API design (v1-v4) for backward compatibility
- AI-powered career path suggestions using LangGraph workflows
- Integration with Neo4j, MongoDB, and Redis databases
- OpenAI and Langfuse integration for AI services
- Comprehensive monitoring with Sentry and structured logging
- Redis-based caching with MongoDB secondary layer

## Architecture

### Database Stack
- **Neo4j** - Graph database for career relationships
- **MongoDB** - Document storage for user profiles
- **Redis** - Caching and session storage

### AI Workflow
LangGraph-based career path generation:
1. `suggest_career_path` - Initial suggestions
2. `evaluate_career_path` - Scoring and evaluation
3. `get_career_path` - Final selection
4. `postprocess_career_path` - Output formatting

## Installation

### Prerequisites
- Python 3.11+
- [uv](https://docs.astral.sh/uv/) package manager
- Docker (optional)
- Neo4j, MongoDB, Redis instances

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd upzi-career-path
   ```

2. **Install dependencies**
   ```bash
   uv sync
   ```

3. **Environment configuration**
   ```bash
   cp .env.dist .env
   # Edit .env with your database connections and API keys
   ```

4. **Start the development server**
   ```bash
   uv run python main.py
   ```

   The API will be available at `http://localhost:8080`

## Development

### Package Management
```bash
# Install new package
uv add <package>

# Install development package
uv add --dev <package>

# Update dependencies
uv sync
```

### Testing
```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov

# Run specific test
uv run pytest tests/test_<module>.py
```

### Code Quality
```bash
# Check linting
uv run ruff check

# Format code
uv run ruff format

# Run pre-commit hooks
pre-commit run --all-files
```

### Docker Deployment
```bash
# Build image
docker build -t upzi-career-path .

# Run container
docker run -p 8080:8080 upzi-career-path
```

## API Versions

The application supports multiple API versions for backward compatibility:

- `/api/v1/` - Base functionality
- `/api/v2/` - Enhanced features
- `/api/v3/` - Advanced capabilities
- `/api/v4/` - Latest version

Each version has corresponding implementations in `app/graph/v*/` directories.

## Configuration

### Required Environment Variables
```bash
# Database connections
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

MONGODB_URL=mongodb://localhost:27017/upzi

REDIS_URL=redis://localhost:6379

# AI Services
OPENAI_API_KEY=your_openai_key
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key

# Application settings
HOST=0.0.0.0
PORT=8080
ENVIRONMENT=development
```

## Monitoring

- **Sentry** - Error tracking and performance monitoring
- **Langfuse** - LLM observability and tracing
- **Structured Logging** - File-based logs with rotation in `logs/`

## Project Structure

```
app/
   api/           # API routers and schemas
   core/          # Core configuration and utilities
   exceptions/    # Custom exceptions
   graph/         # LangGraph workflow implementations
   models/        # Pydantic models
   services/      # External service integrations

tests/             # Test suite
logs/              # Application logs
```

## Contributing

1. Follow the existing code style and patterns
2. Write tests for new functionality
3. Run pre-commit hooks before committing
4. Update documentation as needed

## License

[Add your license information here]
