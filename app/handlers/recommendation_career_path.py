from typing import List, Literal

from langchain_core.runnables import chain
from pydantic import BaseModel

from app.services.llm import get_chat_openai
from app.services.neo4j import get_neo4j_service


class SuggestGroupFunction(BaseModel):
    group_function: List[
        Literal[
            "Accounting",
            "Administrative",
            "Agriculture & Fishery",
            "Arts and Design",
            "Business Development",
            "Construction & Architecture",
            "Consulting",
            "Customer Success and Support",
            "Education",
            "Entrepreneurship",
            "Finance",
            "Government & NGO",
            "Healthcare Services",
            "Hospitality & Food Services",
            "Human Resources",
            "Information Technology",
            "Insurance",
            "Legal",
            "Engineering",
            "Marketing",
            "Military and Protective Services",
            "Operations",
            "Purchasing",
            "Quality Assurance",
            "Real Estate",
            "Research",
            "Retail & Consumer Products",
            "Sales",
            "Transportation",
        ]
    ]


SUGGEST_GROUP_FUNCTION_PROMPT = """You are an AI career counselor tasked with suggesting a career path based on a user's profile. You will be given a user profile containing information about their education, personality, interests, skills, and experience. Your goal is to analyze this information and recommend an appropriate group function for their career.

Here is the user profile:

<user_profile>
{user_profile}
</user_profile>

Consider how the user's background, skills, and interests align with these group functions.

Present your suggestion in the following format:

<group_function>
Suggested group function
</group_function>

Ensure that your suggestion is well-reasoned and tailored to the user's unique profile."""

RECOMMENDATION_CAREER_PATH_PROMPT = """You are an AI career advisor tasked with selecting the most suitable career paths for a user based on their profile. You will be given a user profile and a list of career paths. Your goal is to select the {number_output} career paths that best match the user's profile.

First, review the user profile:

<user_profile>
{user_profile}
</user_profile>

Now, consider the following list of predefined career paths:

<career_paths>
{career_paths}
</career_paths>

Your task is to analyze the user's profile and select the {number_output} most suitable career paths from the provided list. Follow these steps:

1. Analyze the user's profile thoroughly.
2. Consider how the user's background aligns with each career path.
3. Select the {number_output} best-fitting career paths.
4. Present your selection in the specified format.

Before making your final selection, wrap your analysis inside <career_analysis> tags in your thinking block. This will help ensure a thorough and well-reasoned selection. Your analysis should include:

1. Major and University Background:
   - Analyze how the user's educational background relates to each potential career path.
   - Note any specific courses or projects that align with certain careers.

2. Skills Assessment:
   - Evaluate the user's skills and how they align with requirements for different careers.
   - Identify any skill gaps that might affect suitability for certain paths.

3. Work Experience Evaluation:
   - Assess the user's work experience and its relevance to each potential career path.
   - Consider how transferable skills from past roles apply to new career options.

4. Specialized Knowledge:
   - Identify any specialized knowledge or technical abilities that would be valuable in specific roles.
   - Note how this knowledge aligns with the requirements of each career path.

5. Industry-Specific Requirements:
   - Consider how the user's profile matches with industry-specific requirements for each career path.
   - Evaluate any certifications, licenses, or specific experience needed.

6. Career Path Ranking:
   - Explicitly compare and rank the career paths based on the above analysis.
   - Provide a brief justification for the ranking of each path.

7. Final Considerations:
   - Summarize your thoughts on the {number_output} best-fitting career paths based on the above analysis.
   - Highlight key factors that influenced your decision.

After completing your analysis, present your selection of the {number_output} best-fitting career paths in the following format:

<career_paths>
{career_paths_format}
</career_paths>

Remember:
- Only select career paths from the predefined list provided earlier.
- Your final output should consist of only the <career_paths> tag and its contents.
- Do not include any additional commentary, explanations, or justifications outside of the <career_analysis> and <career_paths> tags.
- Your final output should not duplicate or rehash any of the work you did in the thinking block."""


class BestCareerPaths(BaseModel):
    career_paths: List[str]


llm = get_chat_openai(model="gpt-4o-mini")


@chain
async def recommendation_career_path(input: dict):
    major = input.get("major", "")
    number_output = input.get("number_output", 4)
    if not major:
        raise ValueError("Major is required")
    if number_output > 20:
        raise ValueError("Number of output must be less than 20")
    user_profile = f"""Ngành học: {major}"""
    chain_1 = llm.with_structured_output(SuggestGroupFunction)
    prompt = SUGGEST_GROUP_FUNCTION_PROMPT.format(user_profile=f"Ngành học: {major}")
    response = await chain_1.ainvoke(prompt)
    if isinstance(response, SuggestGroupFunction):
        group_function = response.group_function
    else:
        raise ValueError("Invalid response from LLM")

    async with get_neo4j_service() as db:
        query = """MATCH (c:CareerPath)
WHERE c.group_function IN $group_function
RETURN DISTINCT c.name AS career_path"""
        records = await db.execute_query(
            query,
            {
                "group_function": group_function,
            },
        )
        career_paths = [{"career_path": record["career_path"]} for record in records]

    chain_2 = llm.with_structured_output(BestCareerPaths)
    career_paths = ", ".join(career_path["career_path"] for career_path in career_paths)
    # Generate dynamic career paths format
    career_paths_format = "\n".join(
        [f"{i + 1}. [Career path {i + 1}]" for i in range(number_output)]
    )

    prompt = RECOMMENDATION_CAREER_PATH_PROMPT.format(
        user_profile=user_profile,
        career_paths=career_paths,
        number_output=number_output,
        career_paths_format=career_paths_format,
    )
    response = await chain_2.ainvoke(prompt)
    if isinstance(response, BestCareerPaths):
        career_paths = response.career_paths
    else:
        raise ValueError("Invalid response from LLM")
    return {"career_paths": career_paths, "group_function": group_function}
