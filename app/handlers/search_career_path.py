import re
from typing import Any, Dict, <PERSON>, <PERSON><PERSON>

from unidecode import unidecode

from app.api.schemas.career_path import CareerPathSearchRequest
from app.services.neo4j import get_neo4j_service


def prepare_search_queries(query: str | None) -> Tuple[str, str]:
    """
    Prepare search queries by normalizing the input and creating keyword and fulltext queries.

    Args:
        query: Raw search query from user input

    Returns:
        Tuple of (keyword_query, fulltext_query)
    """
    if query:
        # Detect language to determine search field
        # Normalize query by removing accents/diacritics and converting to ASCII
        normalized_query = unidecode(query).lower()

        # For keyword search, use the normalized query
        keyword_query = normalized_query

        # Prepare fulltext query for secondary search
        special_chars = r'[+\-&|!(){}[\]^"~*?:\\\/]'
        clean_query = re.sub(special_chars, lambda m: " ", normalized_query)
        fulltext_query = f"{clean_query}~"  # Apply fuzzy search after cleaning

        return keyword_query, fulltext_query
    else:
        return "", "*"


async def search_career_path(
    data: CareerPathSearchRequest,
) -> <PERSON>ple[List[Dict[str, Any]], int]:
    """
    Search for career path in Neo4j, prioritizing keyword search first and
    falling back to fulltext search for additional results.

    Args:
        data: JobTitleRequest containing search query

    Returns:
        Tuple of (career path list with their details, total count)
    """
    query = data.q
    limit = data.limit or 10
    page = data.page or 1

    # Prepare search queries using the helper function
    keyword_query, fulltext_query = prepare_search_queries(query)

    search_field_vi = "search_name_vi"
    search_field = "search_name"

    async with get_neo4j_service() as db:
        # First try with keyword search with ranking for exact vs contains matches
        keyword_cypher_query = f"""
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE
            (c.{search_field} = $keyword_query OR
             c.{search_field} STARTS WITH $keyword_query OR
             c.{search_field} CONTAINS $keyword_query) OR
             (c.{search_field_vi} = $keyword_query OR
             c.{search_field_vi} STARTS WITH $keyword_query OR
             c.{search_field_vi} CONTAINS $keyword_query) AND
            r.level = "experienced" AND
            r.path_type = "manager"
        WITH c,
             MIN(r.salary_min) as salary_min,
             MAX(r.salary_max) as salary_max,
             HEAD(COLLECT(j.market_outlook)) as market_outlook,
             HEAD(COLLECT(COALESCE(j.salary_currency, "VND"))) as salary_currency,
             HEAD(COLLECT(COALESCE(j.salary_unit, "triệu/tháng"))) as salary_unit
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            c.group_function as group_function,
            salary_min,
            salary_max,
            market_outlook,
            salary_currency,
            salary_unit,
            CASE
                WHEN c.{search_field} = $keyword_query OR c.{search_field_vi} = $keyword_query THEN 15.0  // Exact match gets highest score
                WHEN c.{search_field} STARTS WITH $keyword_query OR c.{search_field_vi} STARTS WITH $keyword_query THEN 12.0  // Starts with gets high score
                ELSE 10.0  // Contains match gets base score
            END as score,
            1 as priority   // Higher priority for keyword results
        """

        # If we have a query and need fulltext search as fallback
        fulltext_cypher_query = """
        CALL db.index.fulltext.queryNodes("career_path_fulltext_index", $fulltext_query)
        YIELD node, score as fulltext_score
        WITH DISTINCT node.name as career_path, fulltext_score
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE
            c.name = career_path AND
            r.level = "experienced" AND
            r.path_type = "manager" AND
            fulltext_score > 4.5
        WITH c, fulltext_score,
             MIN(r.salary_min) as salary_min,
             MAX(r.salary_max) as salary_max,
             HEAD(COLLECT(j.market_outlook)) as market_outlook,
             HEAD(COLLECT(COALESCE(j.salary_currency, "VND"))) as salary_currency,
             HEAD(COLLECT(COALESCE(j.salary_unit, "triệu/tháng"))) as salary_unit
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            c.group_function as group_function,
            salary_min,
            salary_max,
            market_outlook,
            salary_currency,
            salary_unit,
            fulltext_score as score,
            2 as priority   // Lower priority for fulltext results
        """

        # Count query to get total results without pagination
        count_query = f"""
        CALL () {{
            {keyword_cypher_query}
            UNION
            {fulltext_cypher_query}
        }}
        WITH DISTINCT career_path_id
        RETURN COUNT(career_path_id) as total
        """

        # Union query to combine both approaches with proper pagination
        union_query = f"""
        CALL () {{
            {keyword_cypher_query}
            UNION
            {fulltext_cypher_query}
        }}
        WITH DISTINCT career_path_id, career_path, job_function, group_function,
             salary_min, salary_max, market_outlook, salary_currency, salary_unit,
             MAX(score) as score, MIN(priority) as priority
        ORDER BY score DESC, priority ASC, career_path
        SKIP (($page - 1) * $limit)
        LIMIT $limit
        RETURN career_path_id, career_path, job_function, group_function,
               salary_min, salary_max, market_outlook, salary_currency, salary_unit, score
        """

        # Count query for empty search
        empty_count_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE r.level = "experienced" AND r.path_type = "manager"
        RETURN COUNT(DISTINCT c.id) as total
        """

        # Always empty query fallback
        empty_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE r.level = "experienced" AND r.path_type = "manager"
        WITH c,
             MIN(r.salary_min) as salary_min,
             MAX(r.salary_max) as salary_max,
             HEAD(COLLECT(j.market_outlook)) as market_outlook,
             HEAD(COLLECT(COALESCE(j.salary_currency, "VND"))) as salary_currency,
             HEAD(COLLECT(COALESCE(j.salary_unit, "triệu/tháng"))) as salary_unit
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            c.group_function as group_function,
            salary_min,
            salary_max,
            market_outlook,
            salary_currency,
            salary_unit,
            1.0 as score
        ORDER BY c.name
        SKIP (($page - 1) * $limit)
        LIMIT $limit
        """

        # Execute the appropriate query
        if not keyword_query:
            # If no query is provided, return all results
            count_records = await db.execute_query(empty_count_query, {})
            total_count = count_records[0]["total"] if count_records else 0

            records = await db.execute_query(
                empty_query,
                {
                    "limit": limit,
                    "page": page,
                },
            )
        else:
            # Get total count for filtered results
            count_records = await db.execute_query(
                count_query,
                {
                    "keyword_query": keyword_query,
                    "fulltext_query": fulltext_query,
                },
            )
            total_count = count_records[0]["total"] if count_records else 0

            # Use the union query to get both keyword and fulltext results
            records = await db.execute_query(
                union_query,
                {
                    "keyword_query": keyword_query,
                    "fulltext_query": fulltext_query,
                    "limit": limit,
                    "page": page,
                },
            )

        # Format the results (no deduplication needed since Cypher query handles it)
        results = []

        for record in records:
            results.append({
                "career_path_id": record["career_path_id"],
                "career_path": record["career_path"],
                "job_function": record["job_function"],
                "group_function": record["group_function"],
                "salary_range": {
                    "salary_min": record["salary_min"],
                    "salary_max": record["salary_max"],
                    "salary_currency": record["salary_currency"],
                    "salary_unit": record["salary_unit"],
                },
                "market_outlook": record["market_outlook"],
                "score": record["score"],
            })

        return results, total_count
