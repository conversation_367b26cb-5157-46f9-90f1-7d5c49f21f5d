import os

import httpx
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_fixed

load_dotenv()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_user_profile(user_id: str):
    url = os.getenv("USER_PROFILE_SERVICE_URL")
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={"userId": user_id}, timeout=10)
        if response.status_code == 200:
            data = response.json()["data"]
        else:
            return None

    education = []
    for edu in data.get("education", []):
        education.append(
            {
                "university": edu.get("institute", {}).get("name")
                if edu.get("institute", {}).get("name")
                else "",
                "degree": edu.get("degree", {}).get("name")
                if edu.get("degree", {}).get("name")
                else "",
                "major": [major.get("name") for major in edu.get("majors", [])]
                if edu.get("majors", [])
                else [],
                "favourite_subject": [
                    subject.get("name") for subject in edu.get("favoriteSubjects", [])
                ]
                if edu.get("favoriteSubjects", [])
                else [],
            }
        )

    experiences = []
    for exp in data.get("experience", []):
        experiences.append(
            {
                "company": exp.get("companyName") if exp.get("companyName") else "",
                "title": exp.get("titleOriginal") if exp.get("titleOriginal") else "",
                "description": exp.get("description") if exp.get("description") else "",
                "skills": [skill.get("name") for skill in exp.get("skills", [])]
                if exp.get("skills", [])
                else [],
            }
        )

    extra_curriculars = []
    for extra in data.get("extraCurricular", []):
        extra_curriculars.append(
            {
                "organization": extra.get("organizationName")
                if extra.get("organizationName")
                else "",
                "role": extra.get("roleName") if extra.get("roleName") else "",
                "description": extra.get("description")
                if extra.get("description")
                else "",
                "skills": [skill.get("name") for skill in extra.get("skills", [])]
                if extra.get("skills", [])
                else [],
            }
        )

    language_levels = []
    for lang in data.get("language", []):
        language_levels.append(
            {
                "name": lang.get("name") if lang.get("name") else "",
                "level": lang.get("level", {}).get("name")
                if lang.get("level", {}).get("name")
                else "",
            }
        )

    hobbies = []
    for hob in data.get("hobby", []):
        hobbies.append({"name": hob.get("name") if hob.get("name") else ""})

    characteristics = []
    for characteristic in data.get("characteristic", []):
        characteristics.append(
            {"name": characteristic.get("name") if characteristic.get("name") else ""}
        )

    major = ", ".join(
        list(set([major for edu in education for major in edu.get("major", [])]))
    )

    university = ", ".join(list(set([edu.get("university") for edu in education])))

    experience = "\n".join(
        list(
            set(
                [
                    f"Company: {exp.get('company') if exp.get('company') else 'Not provided'} - Job Title: {exp.get('title') if exp.get('title') else 'Not provided'}"
                    for exp in experiences
                ]
            )
        )
    )

    skills_experience = [
        skill for exp in experiences for skill in list(set(exp.get("skills", [])))
    ]

    skills_extra_curricular = [
        skill
        for extra in extra_curriculars
        for skill in list(set(extra.get("skills", [])))
    ]

    skills_set = list(set(skills_experience + skills_extra_curricular))
    skills_gained = ", ".join(skills_set)

    language_level = ", ".join(
        list(
            set(
                [
                    f"{lang.get('name')} - {lang.get('level')}"
                    for lang in language_levels
                ]
            )
        )
    )

    hobbies = ", ".join(list(set([hobby.get("name") for hobby in hobbies])))

    characteristics = ", ".join(
        list(set([characteristic.get("name") for characteristic in characteristics]))
    )

    favourite_subject = ", ".join(
        list(
            set(
                [
                    subject
                    for edu in education
                    for subject in edu.get("favourite_subject", [])
                ]
            )
        )
    )

    return {
        "major": major,
        "university": university,
        "hobbies": hobbies,
        "characteristics": characteristics,
        "skills_gained": skills_gained,
        "experience": experience,
        "favourite_subject": favourite_subject,
        "language_level": language_level,
    }
