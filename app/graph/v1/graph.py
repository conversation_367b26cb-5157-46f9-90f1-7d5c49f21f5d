from langgraph.graph import END, START, StateGraph

from app.graph.v1.nodes import (
    evaluate_career_path,
    get_career_path,
    postprocess_career_path,
    suggest_career_path,
)
from app.graph.v1.state import AgentState, InputState, OutputState

workflow = StateGraph(AgentState, input=InputState, output=OutputState)


def suggest_or_generate(state: AgentState):
    if state.get("career_path"):
        return "evaluate_career_path"
    return "suggest_career_path"


workflow.add_node("suggest_career_path", suggest_career_path)
workflow.add_node("evaluate_career_path", evaluate_career_path)
workflow.add_node("get_career_path", get_career_path)
workflow.add_node("postprocess_career_path", postprocess_career_path)

workflow.add_conditional_edges(
    START, suggest_or_generate, ["suggest_career_path", "evaluate_career_path"]
)
workflow.add_edge("suggest_career_path", "evaluate_career_path")
workflow.add_edge("evaluate_career_path", "get_career_path")
workflow.add_edge("get_career_path", "postprocess_career_path")
workflow.add_edge("postprocess_career_path", END)

graph = workflow.compile()
