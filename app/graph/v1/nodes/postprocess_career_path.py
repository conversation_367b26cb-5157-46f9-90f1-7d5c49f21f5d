import asyncio

from app.graph.v1.state import AgentState

TOP_10_INDUSTRY_TREND = [
    "Applied R&D",
    "Artificial Intelligence",
    "Auditing & Compliance",
    "Brand Management",
    "Buying & Merchandising",
    "Compliance & Risk (incl. Employment/Retirement Law)",
    "Customer Experience",
    "Cybersecurity",
    "Data & Analytics (Data Eng/AI)",
    "Digital Marketing",
    "Digital Transformation",
    "Electrical & Electronics Engineering",
    "Healthcare Administration",
    "IT Infrastructure & Networking",
    "Market Research & Analysis",
    "Marketing Communications",
    "Mechanical Engineering (incl. Automotive)",
    "Operations Management",
    "Product Design & Development",
    "Product Management",
    "Production & Manufacturing",
    "QA & Testing",
    "Quality Control",
    "Retail Management",
    "Risk Consulting",
    "Risk Management",
    "Software Development",
    "Supply Chain & Logistics",
    "Testing & Inspection",
    "UI/UX Design",
]


async def process_career_path(career_path, major, university, evaluates):
    career_path_name = career_path["career_path"]
    career_path_id = career_path["career_path_id"]
    job_function = career_path["job_function"]
    description = career_path["description"]
    skills_needed = career_path["skills_needed"]
    job_progression = career_path["job_progression"]
    group_function = career_path["group_function"]
    current_level = evaluates[career_path_name]["current_level"]
    matching_score = evaluates[career_path_name]["matching_score"]
    why_you_fit = evaluates[career_path_name]["why_you_fit"]

    # Generate mock salary data based on job progression
    salary_data = get_salary_data(job_progression, current_level, major, university)
    market_outlook = get_market_outlook(job_progression, current_level)
    response = {
        "career_path_id": career_path_id,
        "career_path": career_path_name,
        "group_function": group_function,
        "job_function": job_function,
        "matching_score": matching_score,
        "top_industry_trend": 10 if job_function in TOP_10_INDUSTRY_TREND else None,
        "description": description,
        "why_you_fit": why_you_fit,
        "skills_needed": skills_needed,
        "market_outlook": market_outlook,
        "top_suitable_percentage": 4.0,  # TODO: get from top suitable percentage
        "salary": salary_data,
        "statistics": {
            "percentage": 70.0,  # TODO: get from statistics
            "major": major,
            "job_title": career_path_name,
        },
    }
    return response


async def postprocess_career_path(state: AgentState):
    career_paths = state["career_paths"]
    major = state["major"]
    university = state["university"]
    evaluates = state["evaluates"]

    # Process all career paths in parallel
    tasks = [
        process_career_path(career_path, major, university, evaluates)
        for career_path in career_paths
    ]
    responses = await asyncio.gather(*tasks)

    # Sort by matching_score first (descending), then by original index for tiebreaker
    responses_with_index = [(i, response) for i, response in enumerate(responses)]
    responses_with_index.sort(key=lambda x: (-x[1]["matching_score"], x[0]))
    sorted_responses = [response for _, response in responses_with_index]

    return {"responses": sorted_responses}


def get_market_outlook(job_progression, current_level):
    # Find current level in job progression
    current_level_job = None
    standard_progression = job_progression["standard"]

    for i, level in enumerate(standard_progression):
        if level["job_level"] == current_level:
            current_level_job = level
            break

    if not current_level_job:
        # Default to first level if current level not found
        current_level_job = standard_progression[0]
    market_outlook = {
        "number_jobs": current_level_job["market_outlook"],
    }
    return market_outlook


def get_salary_data(job_progression, current_level, major, university):
    salary_unit = "triệu VND/tháng"
    standard_progression = job_progression["standard"]
    expert_progression = job_progression["expert"]
    manager_progression = job_progression["manager"]
    # Map job_level to time to achieve
    time_to_achieve_map = {
        "student": "0 years",
        "entry": "0-1 years",
        "experienced": "1-3 years",
        "senior": "3-5 years",
        "manager": "5-7 years",
        "director_or_above": "7-9 years",
    }

    # Find current level in job progression
    current_level_job = None
    current_level_index = -1

    if current_level == "student":
        salary_data = {
            "current_level": {
                "job_title": "",
                "level": "Student",
                "education": {
                    "major": major,
                    "university": university,
                },
                "salary_range": {
                    "salary_min": 0,
                    "salary_max": 0,
                    "salary_unit": "triệu VND/tháng",
                },
            },
            "standard_level": [],
            "expert_level": [],
            "manager_level": [],
        }
    else:
        for i, level in enumerate(standard_progression):
            if level["job_level"] == current_level:
                current_level_job = level
                current_level_index = i
                break

        if not current_level_job:
            # Default to first level if current level not found
            current_level_job = standard_progression[0]
            current_level_index = 0

        # Generate salary structure
        salary_data = {
            "current_level": {
                "job_title": current_level_job["job_title"],
                "level": current_level_job["job_level"].replace("_", " ").capitalize(),
                "salary_range": {
                    "salary_min": int(current_level_job["salary_min"]),
                    "salary_max": int(current_level_job["salary_max"]),
                    "salary_unit": salary_unit,
                },
            },
            "standard_level": [],
            "expert_level": [],
            "manager_level": [],
        }

    # Generate other levels
    for i, level in enumerate(standard_progression):
        if i > current_level_index:
            level_data = {
                "job_title": level["job_title"],
                "level": level["job_level"].replace("_", " ").capitalize(),
                "salary_range": {
                    "salary_min": int(level["salary_min"]),
                    "salary_max": int(level["salary_max"]),
                    "salary_unit": salary_unit,
                },
                "time_to_achieve": time_to_achieve_map.get(
                    level["job_level"], "1-2 years"
                ),
            }
            salary_data["standard_level"].append(level_data)

    for i, level in enumerate(expert_progression):
        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
        }
        salary_data["expert_level"].append(level_data)

    for i, level in enumerate(manager_progression):
        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
        }
        salary_data["manager_level"].append(level_data)

    return salary_data
