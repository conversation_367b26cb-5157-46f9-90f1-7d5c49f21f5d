from typing import List

from typing_extensions import TypedDict


class InputState(TypedDict):
    career_path: str
    major: str
    university: str
    skills_gained: str
    experience: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str
    number_output: int
    group_function: str


class AgentState(InputState):
    career_paths: List[dict]
    responses: List[dict]


class OutputState(TypedDict):
    responses: List[dict]


class EvalInputState(TypedDict):
    career_path: str
    group_function: str
    major: str
    university: str
    skills_gained: str
    experience: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str


class EvalState(EvalInputState):
    career_path: str
    group_function: str
    current_level: str
    matching_score: int
    why_you_fit: str
    salary: str


class EvalOutputState(TypedDict):
    matching_score: int
    why_you_fit: str
    salary: dict
    statistics: dict
