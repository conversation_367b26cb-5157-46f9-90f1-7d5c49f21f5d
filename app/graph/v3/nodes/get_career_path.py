from app.graph.v3.state import AgentState
from app.services.neo4j import get_neo4j_service


async def get_career_path(state: AgentState):
    initial_group_function = state.get("group_function", "")
    career_paths = state.get("career_paths") or [state["career_path"]]

    list_career_path = []
    async with get_neo4j_service() as neo4j:
        # Process each career path (optimized for batch operations when possible)
        for career_path in career_paths:
            group_function = initial_group_function

            # Get group_function if not provided (optimized: could batch this)
            if not group_function:
                group_function_records = await neo4j.execute_query(
                    "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function",
                    {"career_path": career_path},
                )
                if not group_function_records:
                    raise ValueError(f"Career path '{career_path}' not found")
                group_function = group_function_records[0]["group_function"]

            # Combined query for career path info and skills
            combined_query = """
                MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
                WHERE c.name = $career_path
                AND r.level = "experienced"
                AND c.group_function = $group_function
                WITH c, j
                OPTIONAL MATCH (j)-[:UTILIZES]->(s:SkillV2)
                WITH c.id as career_path_id,
                     c.description as description,
                     c.function as job_function,
                     j.market_outlook as market_outlook,
                     s.type as skill_type,
                     COLLECT(DISTINCT {name: s.name_en, description: s.description}) as skills
                RETURN career_path_id, description, job_function, market_outlook,
                       skill_type, skills
                ORDER BY skill_type
            """

            query_results = await neo4j.execute_query(
                combined_query,
                {"career_path": career_path, "group_function": group_function},
            )

            if not query_results:
                # Try to get the correct group_function for this career path
                correct_group_function_records = await neo4j.execute_query(
                    "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN DISTINCT c.group_function as group_function",
                    {"career_path": career_path},
                )

                if correct_group_function_records:
                    # Retry with the correct group_function
                    correct_group_function = correct_group_function_records[0][
                        "group_function"
                    ]
                    if correct_group_function != group_function:
                        # Update group_function and retry the query
                        group_function = correct_group_function
                        query_results = await neo4j.execute_query(
                            combined_query,
                            {
                                "career_path": career_path,
                                "group_function": group_function,
                            },
                        )

                        if not query_results:
                            raise ValueError(
                                f"No experienced job title found for career path '{career_path}'"
                            )
                    else:
                        raise ValueError(
                            f"No experienced job title found for career path '{career_path}' with group function '{group_function}'"
                        )
                else:
                    raise ValueError(f"Career path '{career_path}' not found")

            # Process results - first record has career path info
            first_record = query_results[0]
            data = {
                "career_path": career_path,
                "group_function": group_function,
                "career_path_id": first_record["career_path_id"],
                "description": first_record["description"],
                "job_function": first_record["job_function"],
                "market_outlook": first_record["market_outlook"],
            }

            # Process skills from all records
            skills_by_type = {}
            for record in query_results:
                if record["skill_type"] and record["skills"]:
                    skills_by_type[record["skill_type"]] = record["skills"]

            data["skills_needed"] = {
                "technical_skills": skills_by_type.get("hard", [])[:4],
                "soft_skills": skills_by_type.get("soft", [])[:4],
            }

            if not any(data["skills_needed"].values()):
                # Skills might be empty if no skills are associated
                data["skills_needed"] = {"technical_skills": [], "soft_skills": []}

            list_career_path.append(data)

    return {"career_paths": list_career_path}
