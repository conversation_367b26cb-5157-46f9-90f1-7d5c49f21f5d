from typing import List, Literal

from dotenv import load_dotenv
from pydantic import BaseModel

from app.graph.v3.state import InputState
from app.services.llm import get_chat_openai
from app.services.neo4j import get_neo4j_service

load_dotenv()

llm = get_chat_openai(model="gpt-4o", temperature=0)


class SuggestGroupFunction(BaseModel):
    major_based_group: List[
        Literal[
            "Accounting",
            "Administrative",
            "Agriculture & Fishery",
            "Arts and Design",
            "Business Development",
            "Construction & Architecture",
            "Consulting",
            "Customer Success and Support",
            "Education",
            "Entrepreneurship",
            "Finance",
            "Government & NGO",
            "Healthcare Services",
            "Hospitality & Food Services",
            "Human Resources",
            "Information Technology",
            "Insurance",
            "Legal",
            "Engineering",
            "Marketing",
            "Military and Protective Services",
            "Operations",
            "Purchasing",
            "Quality Assurance",
            "Real Estate",
            "Research",
            "Retail & Consumer Products",
            "Sales",
            "Transportation",
        ]
    ]
    experience_based_group: (
        List[
            Literal[
                "Accounting",
                "Administrative",
                "Agriculture & Fishery",
                "Arts and Design",
                "Business Development",
                "Construction & Architecture",
                "Consulting",
                "Customer Success and Support",
                "Education",
                "Entrepreneurship",
                "Finance",
                "Government & NGO",
                "Healthcare Services",
                "Hospitality & Food Services",
                "Human Resources",
                "Information Technology",
                "Insurance",
                "Legal",
                "Engineering",
                "Marketing",
                "Military and Protective Services",
                "Operations",
                "Purchasing",
                "Quality Assurance",
                "Real Estate",
                "Research",
                "Retail & Consumer Products",
                "Sales",
                "Transportation",
            ]
        ]
        | None
    ) = None


SUGGEST_GROUP_FUNCTION_PROMPT = """You are an AI career counselor tasked with suggesting career group functions based on a user's profile. You will analyze the user's educational background and work experience to suggest appropriate group functions.

Here is the user profile:

<user_profile>
{user_profile}
</user_profile>

Your task is to suggest group functions based on two criteria:

1. **Major-based group function**: Analyze the user's educational background (major) and suggest the most appropriate group function that aligns with their field of study.

2. **Experience-based group function**: If the user has work experience, analyze their last/most recent job title and suggest the most appropriate group function that aligns with their professional experience. If the user has no work experience or the experience field is empty/minimal, set this to null.

For the experience analysis, focus on:
- The most recent or last job title mentioned in their experience
- The industry or field they worked in
- The type of work they performed

Present your suggestions in the following format:

<major_based_group>
Group function based on major
</major_based_group>

<experience_based_group>
Group function based on experience (or null if no relevant experience)
</experience_based_group>

Ensure your suggestions are well-reasoned and based on clear alignment between the user's background and the available group functions."""

SUGGEST_CAREER_PATH_PROMPT = """You are an AI career advisor tasked with suggesting career paths that match a given user profile. Your goal is to analyze the user's background and select the most suitable career options from a predefined list.

First, carefully examine the user profile:

<user_profile>
{user_profile}
</user_profile>

Now, review the list of available career paths:

<career_paths>
{career_paths}
</career_paths>

Your task is to select the top {number_output} career paths that best match this user profile. Follow these steps:

1. Analyze the user profile thoroughly, considering:
   - Major
   - Skills
   - Experience
   - Educational background
   - Language requirements
   - Hobbies and characteristics
   - Favorite subjects

2. Compare each career path in the list against the user profile, evaluating:
   - Relevance to the user's major
   - Alignment with the user's skills
   - Compatibility with the user's experience
   - Fit with the user's educational background
   - Match with language requirements
   - Potential alignment with hobbies and characteristics
   - Relation to favorite subjects

3. Select the top {number_output} best-matching career paths based on your analysis.

4. **Sort the selected career paths by their compatibility scores in descending order (highest score first).**

5. For each selected career path, provide a brief explanation of why it's a good match for the user.

6. Format your output as a list of career paths, each with its explanation, using the following structure:

<career_paths>
{career_paths_format}
</career_paths>

Before providing your final output, wrap your analysis inside <career_analysis> tags in your thinking block. In this analysis:

1. List out the key aspects of the user profile.
2. For each career path:
   - Rate its compatibility with the user profile on a scale of 1-10 for each aspect.
   - Sum up these scores to get an overall compatibility score.
3. Explain your reasoning for selecting the top matches based on these scores.

This will ensure a thorough and transparent selection process. It's OK for this section to be quite long.

Remember, your final output must strictly adhere to the format specified above, listing only the selected career paths with their explanations. Do not duplicate or rehash any of the work you did in the career analysis section."""


class BestCareerPaths(BaseModel):
    career_paths: List[str]


async def suggest_career_path(state: InputState):
    user_profile = f"""Ngành học: {state["major"]}
Trường đại học: {state["university"]}
Kinh nghiệm làm việc: {state["experience"]}
Sở thích: {state["hobbies"]}
Tính cách: {state["characteristics"]}
Môn học yêu thích: {state["favourite_subject"]}
Trình độ ngoại ngữ: {state["language_level"]}"""
    suggest_group_function_chain = llm.with_structured_output(SuggestGroupFunction)
    prompt = SUGGEST_GROUP_FUNCTION_PROMPT.format(
        user_profile=f"Ngành học: {state['major']}\nKinh nghiệm làm việc: {state['experience']}"
    )
    response = await suggest_group_function_chain.ainvoke(prompt)
    if isinstance(response, SuggestGroupFunction):
        major_based_group = response.major_based_group
        experience_based_group = response.experience_based_group
    else:
        raise ValueError("Invalid response from LLM")

    # Collect unique group functions to query
    group_functions = (
        major_based_group
        if isinstance(major_based_group, list)
        else [major_based_group]
    )
    if experience_based_group and experience_based_group not in major_based_group:
        group_functions.extend(experience_based_group)

    async with get_neo4j_service() as db:
        # Optimize: Use single query with IN clause instead of N+1 queries
        all_group_functions = group_functions.copy()

        # Always include IT career paths if not already in the list
        if "Information Technology" not in all_group_functions:
            all_group_functions.append("Information Technology")

        # Single batched query to get all career paths for all group functions
        batch_query = """
            MATCH (c:CareerPath)
            WHERE c.group_function IN $group_functions
            RETURN DISTINCT c.name AS career_path, c.group_function AS group_function
            ORDER BY c.name
        """

        records = await db.execute_query(
            batch_query, {"group_functions": all_group_functions}
        )

        # Convert to the expected format and remove duplicates while preserving order
        seen = set()
        career_paths = []
        for record in records:
            career_path_name = record["career_path"]
            if career_path_name not in seen:
                seen.add(career_path_name)
                career_paths.append({"career_path": career_path_name})

    suggest_career_path_chain = llm.with_structured_output(BestCareerPaths)
    career_paths_str = "\n".join(
        "- " + career_path["career_path"] for career_path in career_paths
    )
    # Generate dynamic career paths format
    career_paths_format = "\n".join(
        [f"[Career path {i + 1}]" for i in range(state.get("number_output", 4))]
    )

    prompt = SUGGEST_CAREER_PATH_PROMPT.format(
        user_profile=user_profile,
        career_paths=career_paths_str,
        number_output=state.get("number_output", 4),
        career_paths_format=career_paths_format,
    )
    response = await suggest_career_path_chain.ainvoke(prompt)
    if isinstance(response, BestCareerPaths):
        final_career_paths = [
            career_path
            for career_path in response.career_paths
            if career_path in career_paths_str
        ]
    else:
        raise ValueError("Invalid response from LLM")
    return {"career_paths": final_career_paths}
