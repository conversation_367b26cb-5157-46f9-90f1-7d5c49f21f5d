import asyncio
import math
from enum import Enum
from typing import List

from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate
from pydantic import BaseModel, Field

from app.graph.v3.state import EvalState
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.llm import get_chat_openai
from app.services.neo4j import get_neo4j_service

load_dotenv()

llm = get_chat_openai(model="gpt-4o")


class Criterion(Enum):
    MAJOR = "Major"
    UNIVERSITY = "University"
    FAVOURITE_SUBJECT = "Favourite subject"
    SKILLS_GAINED = "Skills gained"
    EXPERIENCE = "Experience"
    HOBBIES = "Hobbies"
    CHARACTERISTICS = "Characteristics"
    LANGUAGE_LEVEL = "Language level"


class CriterionWeightsNoExperience(Enum):
    MAJOR = 35
    UNIVERSITY = 15
    SKILLS_GAINED = 20
    FAVOURITE_SUBJECT = 10
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 5
    LANGUAGE_LEVEL = 5


class CriterionWeightsEntryLevel(Enum):
    MAJOR = 20
    UNIVERSITY = 10
    SKILLS_GAINED = 20
    FAVOURITE_SUBJECT = 5
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 30
    LANGUAGE_LEVEL = 5


class CriterionWeightsExperienced(Enum):
    MAJOR = 15
    UNIVERSITY = 5
    SKILLS_GAINED = 25
    FAVOURITE_SUBJECT = 5
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 35
    LANGUAGE_LEVEL = 5


class Evaluation(BaseModel):
    criterion: Criterion
    score: int


job_level_mapping = {
    "Student": "student",
    "Entry": "entry",
    "Experienced": "experienced",
    "Senior": "senior",
    "Expert": "expert",
    "Manager": "manager",
    "Director or above": "director_or_above",
}

JobLevel = Enum("JobLevel", job_level_mapping)


class EvaluationResult(BaseModel):
    evaluation: List[Evaluation]
    current_level: JobLevel = Field(
        description="The current level of the user profile in the job title"
    )
    has_experience: bool = Field(
        description="Boolean indicating if user has relevant work experience in the target career path"
    )
    experience_segment: str = Field(
        description="Experience segment: 'no_experience', 'entry_level', or 'experienced_level'"
    )
    time_period: str = Field(
        description="Detected time period from experience text (e.g., '2 years', '6 months', 'no experience')"
    )


class EvaluationSection(BaseModel):
    evaluation: List[Evaluation] = Field(
        description="List of evaluations for each criterion with scores 0-10 or -1 for no information"
    )


class CurrentLevelSection(BaseModel):
    current_level: JobLevel = Field(
        description="The current level of the user profile in the career path"
    )


class ExperienceSection(BaseModel):
    has_experience: bool = Field(
        description="Boolean indicating if user has relevant work experience in the target career path"
    )
    experience_segment: str = Field(
        description="Experience segment: 'no_experience', 'entry_level', or 'experienced_level'"
    )
    time_period: str = Field(
        description="Detected time period from experience text (e.g., '2 years', '6 months', 'no experience')"
    )


class ScoreResponse(BaseModel):
    generic_info: str = Field(description="Short title/summary in markdown format")
    detailed_info: str = Field(
        description="Full detailed assessment in markdown format with all sections"
    )


HIGH_SCORE_PROMPT = """# System Prompt for Job Matching Response Generator

You are a career advisor assistant that creates personalized job matching assessments for candidates. When provided with a user's profile and a job position, you will generate a concise evaluation of their fit for the role.

## Response Format Requirements:
- Write in Vietnamese using markdown formatting
- Return the response in JSON format with two fields: generic_info and detailed_info
- Both fields should contain markdown content that represents the V2 JSON block structure

## Content Requirements for Matching Score >= 50%:

### generic_info field:
- Generate markdown that represents the V2 JSON block structure
- Should contain a title and paragraph content
- Format as markdown equivalent of: {"title": "...", "content": "..."}

### detailed_info field:
- Begin with the matching percentage between the user and career path
- Structure the response in 4 specific sections:

1. **Bạn phù hợp với hướng đi này vì:**
   - 3 bullet points
   - Based on key factors: Education (if no experience segment), Experience (if entry/experienced segment), or Skills gained
   - Focus on strongest matching aspects from user profile

2. **Bạn sẽ cần bổ sung thêm:**
   - 3-4 bullet points
   - Focus on Skill gaps, Experience gaps, or Characteristics gaps
   - Provide specific, actionable improvement areas

3. **Next steps:**
   - 3-4 bullet points
   - Include Ngắn hạn, Dài hạn, Ứng dụng khi tìm việc
   - Provide concrete next steps for career development

4. **Top 3 công ty tại Việt Nam nè**
   - List exactly 3 top companies in Vietnam for this career path
   - Focus on well-known companies in the overall Vietnamese market, not filtered by user experience level
   - Present as a simple comma-separated list

## Tone and Style:
- Professional but warm and encouraging
- Use markdown formatting for flexibility
- Focus on practical, actionable advice
- Maintain positive tone while being realistic about gaps

## Example response format:

```json
{
  "generic_info": "# Bạn có nền tảng tốt cho vị trí Product Manager\n\nVới sự kết hợp giữa nền tảng kinh doanh, kỹ năng phân tích và đam mê công nghệ, bạn đã sẵn sàng để phát triển thành một Product Manager thành công!",
  "detailed_info": "Độ phù hợp giữa bạn và Product Manager là **78%**\n\n**Bạn phù hợp với hướng đi này vì:**\n- Nền tảng Quản trị Kinh doanh tại Đại học Ngoại thương cung cấp kiến thức về chiến lược và phân tích thị trường cần thiết cho Product Manager\n- Kỹ năng phân tích dữ liệu và tư duy logic từ kinh nghiệm thực tập giúp đưa ra quyết định sản phẩm dựa trên insights\n- Sở thích về công nghệ và theo dõi trends giúp nắm bắt nhu cầu thị trường và đổi mới sản phẩm\n\n**Bạn sẽ cần bổ sung thêm:**\n- Hiểu biết sâu hơn về UX/UI design và nghiên cứu người dùng để thiết kế sản phẩm tốt hơn\n- Kỹ năng quản lý dự án Agile/Scrum để điều phối team phát triển hiệu quả\n- Kinh nghiệm thực tế trong việc launch và theo dõi metrics sản phẩm\n- Kỹ năng thuyết trình và storytelling để truyền đạt vision sản phẩm\n\n**Next steps:**\n- **Ngắn hạn:** Học các khóa về Product Management cơ bản, UX/UI fundamentals trên Coursera hoặc Udemy\n- **Dài hạn:** Tìm vị trí Product Analyst hoặc thực tập Product Management để tích lũy kinh nghiệm thực tế\n- **Ứng dụng khi tìm việc:** Tạo case study phân tích sản phẩm hiện có và đề xuất cải tiến để build portfolio. Target vào các startup tech Việt Nam nơi dễ tiếp cận role PM hơn\n\n**Top 3 công ty tại Việt Nam nè**\nVNG, Shopee, FPT Software"
}
```

Always maintain this structure regardless of the specific job or candidate profile. The response should be immediately actionable and help the candidate understand their suitability for the position."""

LOW_SCORE_PROMPT = """# System Prompt for Low Job Matching Response Generator

You are a career advisor assistant that creates personalized job matching assessments for candidates. When a user's profile has a low matching score (below 50%) for a job position, you will generate a constructive evaluation that identifies gaps while offering alternatives.

## Response Format Requirements:
- Write in Vietnamese using markdown formatting
- Return the response in JSON format with two fields: generic_info and detailed_info
- Both fields should contain markdown content that represents the V2 JSON block structure

## Content Requirements for Matching Score < 50%:

### generic_info field:
- Generate markdown that represents the V2 JSON block structure
- Should contain a title and paragraph content
- Format as markdown equivalent of: {"title": "...", "content": "..."}

### detailed_info field:
- Begin with the matching percentage between the user and career path
- Structure the response in 3 specific sections:

1. **Hướng đi này sẽ hơi khó khăn đó:**
   - 3-4 bullet points
   - [Acknowledge challenge] + [Explain main gap] + [But show it's possible]
   - Be honest but encouraging about the challenges

2. **Bạn sẽ cần bổ sung thêm:**
   - 4-5 bullet points
   - Foundation → Skills → Experience → Mindset
   - Provide specific, actionable improvement areas in logical progression

3. **Bạn có thể cân nhắc thêm các hướng đi sau:**
   - Show other matching career paths, sorted by relevance
   - Focus on alternatives that better match their current profile
   - Provide 3-4 alternative career suggestions with brief explanations

## Tone and Style:
- Constructive and supportive, never discouraging
- Honest about challenges without being negative
- Use markdown formatting for flexibility
- Balance candid assessment with actionable recommendations
- Show that the path is difficult but not impossible

## Example response format:

```json
{
  "generic_info": "# Bạn có thể chưa thực sự phù hợp với vị trí Product Manager lúc này, cân nhắc thêm nha\n\nHãy cân nhắc các vị trí như Lập trình viên phần mềm, Kỹ thuật viên mạng hoặc Chuyên viên hỗ trợ kỹ thuật, nơi chuyên ngành Công nghệ thông tin của bạn được sử dụng hiệu quả hơn. Tuy nhiên, điểm số phù hợp chỉ là tham khảo. Nếu bạn thực sự đam mê quản lý sản phẩm, hãy bắt đầu trang bị kiến thức và kỹ năng cần thiết qua các khóa đào tạo, chứng chỉ về quản lý sản phẩm và thực hành các dự án thực tế",
  "detailed_info": "Độ phù hợp giữa bạn và Product Manager là **38%**\n\n**Hướng đi này sẽ hơi khó khăn đó:**\n- Product Manager đòi hỏi sự kết hợp giữa tư duy kinh doanh, kỹ năng kỹ thuật và hiểu biết về trải nghiệm người dùng - những mảng hiện tại chưa thực sự mạnh trong profile của bạn\n- Thiếu kinh nghiệm thực tế trong việc quản lý sản phẩm, làm việc với stakeholders và ra quyết định dựa trên data sẽ là rào cản lớn khi apply vào các vị trí PM\n- Tuy nhiên, nền tảng kỹ thuật của bạn là lợi thế lớn - nhiều PM thành công xuất phát từ background technical\n- Với roadmap rõ ràng và kiên trì, bạn hoàn toàn có thể chuyển sang PM trong 1-2 năm tới\n\n**Bạn sẽ cần bổ sung thêm:**\n- **Foundation:** Học business fundamentals, marketing cơ bản, và customer psychology để hiểu được business logic\n- **Skills:** Nắm vững các tools như Figma, analytics platforms, và project management software (Jira, Notion)\n- **Experience:** Tìm cơ hội làm side project hoặc freelance các dự án có element product để build portfolio\n- **Mindset:** Phát triển customer-centric thinking và khả năng đưa ra quyết định trong môi trường uncertainty cao\n- **Network:** Kết nối với PM community Việt Nam, tham gia các meetup và workshop để học hỏi kinh nghiệm\n\n**Bạn có thể cân nhắc thêm các hướng đi sau:**\n- **Software Engineer/Developer** - tận dụng nền tảng kỹ thuật mạnh, có thể transition sang Technical PM sau này\n- **Data Analyst/Business Analyst** - stepping stone tốt để học business logic và làm quen với product metrics\n- **QA Engineer** - gần gũi với product development process, dễ hiểu user experience và product quality\n- **Technical Support/Customer Success** - học cách hiểu customer pain points, foundation quan trọng cho PM"
}
```

Always maintain this structure regardless of the specific job or candidate profile. The response should help the candidate understand why there's a lower match while providing constructive guidance on potential next steps and realistic alternatives."""

# Split prompts for parallel processing
EVALUATION_PROMPT = """You are an AI assistant specialized in career matching. Your task is to evaluate how well a user's profile matches a specific career path based on given criteria. Please analyze the following information carefully:

<user_profile>
{user_profile}
</user_profile>

The career path to evaluate against is:
<career_path>
{career_path}
</career_path>

Before providing your evaluation, please think through the process carefully. Use the following <analysis> tags inside your thinking block to show your analysis:

<analysis>
1. Extract key information from the user's profile:
   - Education background
   - Personality traits
   - Interests
   - Skills
   - Favorite subjects
   - Work experience
   - Language proficiency

2. List out the requirements and characteristics of the specified career path.

3. Compare the user's profile with the career path requirements:
   For each of the following criteria, note how well the user's attributes match the career requirements:
   a. Ngành học phù hợp với công việc
   b. Trường đại học danh tiếng về ngành học
   c. Tính cách
   d. Sở thích
   e. Kỹ năng phù hợp với công việc
   f. Môn học yêu thích phù hợp với công việc
   g. Kinh nghiệm làm việc phù hợp với công việc
   h. Trình độ ngoại ngữ
</analysis>

After your analysis, provide your evaluation using the following format:

<evaluation>
Criterion: [criterion_name]
Score: [0-10, or -1 if criterion is empty (Không có thông tin)]
</evaluation>

Please provide your evaluation for all criteria, following the format specified above. Your final output should consist only of the evaluation and should not duplicate or rehash any of the work you did in the analysis section."""

CURRENT_LEVEL_PROMPT = """You are an AI assistant specialized in analyzing career levels. Your task is to determine the current job level of a user by comparing job titles in their working experience with the target career path.

<user_profile>
{user_profile}
</user_profile>

<career_path>
{career_path}
</career_path>

Analyze the user's current job level by comparing job titles in their working experience:

**Job Title Comparison Analysis:**
1. Extract all job titles from the user's working experience
2. Compare each job title with the target career path
3. Determine if there's an exact match

**Job Title Matching Rules:**
- **Exact Match**: Same job title OR similar domain/function (e.g., Software Engineer, Frontend Engineer, Backend Engineer) → Use seniority level from title
- **Unrelated Match**: Different domain/function that doesn't match career path → Default to "entry" regardless of previous seniority

**Examples:**
- DevOps Engineer experience + Sales Engineer career path → "entry" (different domains)
- Junior Software Engineer experience + Software Engineer career path → "entry" (exact match, junior level)
- Senior Product Manager experience + Product Manager career path → "senior" (exact match, senior level)
- Marketing Manager experience + Software Engineer career path → "entry" (different domains)
- Frontend Engineer experience + Software Engineer career path → Use seniority level (same domain)
- Senior Backend Developer experience + Software Engineer career path → "senior" (same domain)

**Decision Logic:**
- If no working experience → "student"
- If job titles are from different domain/function → "entry"
- If job titles match career path (exact or same domain) → Use seniority level from title
- If multiple matches → Use most recent or highest level

**Available levels**: "student", "entry", "experienced", "senior", "expert", "manager", "director_or_above"

Return the most accurate single level based on job title comparison with career path."""

EXPERIENCE_ANALYSIS_PROMPT = """You are an AI assistant specialized in analyzing work experience relevance. Your task is to analyze the user's work experience and determine its relevance to the target career path.

<user_profile>
{user_profile}
</user_profile>

<career_path>
{career_path}
</career_path>

Analyze the user's work experience and provide:

1. **has_experience**: Return true if the user has work experience that is directly relevant to the target career path. This should be an exact or very close match between their job titles/roles and the career path. Return false if they have no relevant experience in this specific career path.

2. **experience_segment**: Classify the experience level:
   - "no_experience": No work experience OR experience in completely different field
   - "entry_level": Relevant experience with junior titles, 0-2 years in target field
   - "experienced_level": 2+ years relevant experience, Senior/lead titles, Independent contributor roles

3. **time_period**: Extract the specific time period mentioned in the experience text. Examples: "2 years", "6 months", "1 năm", "3 tháng", "no experience", "chưa có kinh nghiệm". If no specific time is mentioned but experience exists, estimate based on job titles and context.

Focus on experience that is directly relevant to the target career path. General work experience in unrelated fields should not count as relevant experience."""


def process_evaluation(evaluation):
    total_score = 0
    total_weight = 0

    # Determine which weights to use based on experience segment
    experience_segment = evaluation.get("experience_segment", "no_experience")

    if experience_segment == "no_experience":
        weights_enum = CriterionWeightsNoExperience
    elif experience_segment == "entry_level":
        weights_enum = CriterionWeightsEntryLevel
    else:  # experienced_level
        weights_enum = CriterionWeightsExperienced

    for data in evaluation["evaluation"]:
        criterion = data["criterion"]
        score = data["score"]
        weight = weights_enum[Criterion(criterion).name].value
        if score != -1:
            total_weight += weight
            total_score += score * weight / 10
        else:
            total_score += 0
    if total_weight == 0:
        return 0
    overall_score = int(math.ceil(total_score * 100 / total_weight))
    return overall_score


async def evaluate_parallel_sections(user_profile: str, career_path: str):
    """
    Evaluate user profile against career path using parallel processing for 3 sections:
    1. Evaluation scoring (8 criteria)
    2. Current level analysis
    3. Experience analysis
    """
    # Create prompts for each section
    evaluation_prompt = ChatPromptTemplate.from_template(template=EVALUATION_PROMPT)
    current_level_prompt = ChatPromptTemplate.from_template(
        template=CURRENT_LEVEL_PROMPT
    )
    experience_analysis_prompt = ChatPromptTemplate.from_template(
        template=EXPERIENCE_ANALYSIS_PROMPT
    )

    # Create chains with structured output
    evaluation_chain = evaluation_prompt | llm.with_structured_output(EvaluationSection)
    current_level_chain = current_level_prompt | llm.with_structured_output(
        CurrentLevelSection
    )
    experience_analysis_chain = experience_analysis_prompt | llm.with_structured_output(
        ExperienceSection
    )

    # Create input data for all sections
    section_input = {
        "user_profile": user_profile,
        "career_path": career_path,
    }

    # Execute all sections in parallel
    evaluation_result, current_level_result, experience_result = await asyncio.gather(
        evaluation_chain.ainvoke(section_input),
        current_level_chain.ainvoke(section_input),
        experience_analysis_chain.ainvoke(section_input),
    )

    # Combine results into the original format
    combined_result = {
        "evaluation": [
            eval_item.model_dump() for eval_item in evaluation_result.evaluation
        ],
        "current_level": current_level_result.current_level,
        "has_experience": experience_result.has_experience,
        "experience_segment": experience_result.experience_segment,
        "time_period": experience_result.time_period,
    }

    return combined_result


async def evaluate_career_path(state: EvalState):
    if not state.get("group_function"):
        group_function_query = "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function"
        async with get_neo4j_service() as neo4j:
            group_function_records = await neo4j.execute_query(
                group_function_query,
                {"career_path": state["career_path"]},
            )
            if len(group_function_records) > 0:
                group_function = group_function_records[0]["group_function"]
            else:
                raise ValueError(f"No group function found - {state['career_path']}")
    else:
        group_function = state["group_function"]
    user_profile = f"""Ngành học: {state["major"] if state["major"] else "Không có thông tin"}
Trường đại học: {state["university"] if state["university"] else "Không có thông tin"}
Tính cách: {state["characteristics"] if state["characteristics"] else "Không có thông tin"}
Sở thích: {state["hobbies"] if state["hobbies"] else "Không có thông tin"}
Kỹ năng : {state["skills_gained"] if state["skills_gained"] else "Không có thông tin"}
Môn học yêu thích: {state["favourite_subject"] if state["favourite_subject"] else "Không có thông tin"}
Kinh nghiệm làm việc: {state["experience"] if state["experience"] else "Không có thông tin"}
Trình độ ngoại ngữ: {state["language_level"] if state["language_level"] else "Không có thông tin"}"""
    # Generate cache key
    cache_data = {
        "career_path": state["career_path"],
        "major": state["major"],
        "university": state["university"],
        "characteristics": state["characteristics"],
        "hobbies": state["hobbies"],
        "skills_gained": state["skills_gained"],
        "favourite_subject": state["favourite_subject"],
        "experience": state["experience"],
        "language_level": state["language_level"],
    }
    cache_key = generate_cache_key("matching_score", cache_data)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result is not None and "score" in cached_result:
        matching_score = cached_result["score"]
        # We still need to get the evaluation for current_level using parallel processing
        evaluation = await evaluate_parallel_sections(
            user_profile, state["career_path"]
        )
    else:
        # If not in cache, calculate it using parallel processing
        evaluation = await evaluate_parallel_sections(
            user_profile, state["career_path"]
        )
        matching_score = process_evaluation(evaluation)

        # Store in cache (ignore failures)
        await cache_set(cache_key, {"score": matching_score})

    if matching_score >= 50:
        high_score_template = ChatPromptTemplate.from_messages(
            messages=[
                ("system", HIGH_SCORE_PROMPT),
                (
                    "user",
                    "Lộ trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
                ),
            ],
            template_format="jinja2",
        )
        high_score_chain = high_score_template | llm.with_structured_output(
            ScoreResponse
        )
        response: ScoreResponse = await high_score_chain.ainvoke(
            {
                "matching_score": matching_score,
                "user_profile": user_profile,
                "career_path": state["career_path"],
            }
        )
        why_you_fit = {
            "generic_info": response.generic_info,
            "detailed_info": response.detailed_info,
        }
    else:
        low_score_template = ChatPromptTemplate.from_messages(
            messages=[
                ("system", LOW_SCORE_PROMPT),
                (
                    "user",
                    "Lộ trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
                ),
            ],
            template_format="jinja2",
        )
        low_score_chain = low_score_template | llm.with_structured_output(ScoreResponse)
        response: ScoreResponse = await low_score_chain.ainvoke(
            {
                "matching_score": matching_score,
                "user_profile": user_profile,
                "career_path": state["career_path"],
            }
        )
        why_you_fit = {
            "generic_info": response.generic_info,
            "detailed_info": response.detailed_info,
        }
    return {
        "career_path": state["career_path"],
        "group_function": group_function,
        "current_level": evaluation["current_level"].value,
        "matching_score": matching_score,
        "why_you_fit": why_you_fit,
        "experience_segment": evaluation.get("experience_segment", "no_experience"),
        "time_period": evaluation.get("time_period", "Không có thông tin"),
    }
