import asyncio
import math

from app.graph.v4.chains.current_level import current_level_chain
from app.graph.v4.chains.evaluation import evaluation_chain
from app.graph.v4.chains.experience_analysis import experience_analysis_chain
from app.graph.v4.chains.generic_info import get_generic_info
from app.graph.v4.models import (
    Criterion,
    CriterionWeightsEntryLevel,
    CriterionWeightsExperienced,
    CriterionWeightsNoExperience,
)
from app.services.cache import cache_get, cache_set, generate_cache_key


def process_evaluation(evaluation):
    total_score = 0
    total_weight = 0

    # Determine which weights to use based on experience segment
    experience_segment = evaluation.get("experience_segment", "no_experience")

    if experience_segment == "no_experience":
        weights_enum = CriterionWeightsNoExperience
    elif experience_segment == "entry_level":
        weights_enum = CriterionWeightsEntryLevel
    else:  # experienced_level
        weights_enum = CriterionWeightsExperienced

    for data in evaluation["evaluation"]:
        criterion = data["criterion"]
        score = data["score"]
        weight = weights_enum[Criterion(criterion).name].value
        if score != -1:
            total_weight += weight
            total_score += score * weight / 10
        else:
            total_score += 0
    if total_weight == 0:
        return 0
    overall_score = int(math.ceil(total_score * 100 / total_weight))
    return overall_score


async def calculate_matching_score(career_path, user_profile):
    """Calculate matching score for a single career path using optimized parallel processing with Redis caching"""
    try:
        # Generate cache key from career path and user profile
        cache_data = {"career_path": career_path, "user_profile": user_profile}
        cache_key = generate_cache_key("matching_score", cache_data)

        # Try to get cached result
        cached_result = await cache_get(cache_key)
        if cached_result is not None:
            return (
                cached_result.get("score", 0),
                cached_result.get("current_level", "experienced"),
                cached_result.get("generic_info", ""),
            )

        # Create input data for evaluation
        section_input = {
            "user_profile": user_profile,
            "career_path": career_path,
        }

        # Execute sections in parallel (without evaluation_chain)
        (
            evaluation_result,
            experience_result,
            current_level_result,
        ) = await asyncio.gather(
            evaluation_chain.ainvoke(section_input),
            experience_analysis_chain.ainvoke(section_input),
            current_level_chain.ainvoke(section_input),
        )

        # Convert to the format expected by process_evaluation
        evaluation_data = {
            "evaluation": [
                eval_item.model_dump() for eval_item in evaluation_result.evaluation
            ],
            "experience_segment": experience_result.experience_segment,
        }

        # Calculate matching score using the proper experience segment
        matching_score = process_evaluation(evaluation_data)

        # get generic info
        generic_info = await get_generic_info(career_path, user_profile, matching_score)

        # convert current_level to string
        current_level = current_level_result.current_level.value

        # Cache the result
        await cache_set(
            cache_key,
            {
                "score": matching_score,
                "current_level": current_level,
                "generic_info": generic_info,
            },
        )

        return matching_score, current_level, generic_info
    except Exception:
        # Return 0 if evaluation fails
        return 0, "experienced", ""
