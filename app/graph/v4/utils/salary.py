"""Salary-related utility functions."""


def get_comprehensive_salary_data(job_progression, current_level, major, university):
    """Generate comprehensive salary data with progression paths"""
    salary_unit = "triệu VND/tháng"
    standard_progression = job_progression["standard"]
    expert_progression = job_progression["expert"]
    manager_progression = job_progression["manager"]

    # Map job_level to time to achieve
    time_to_achieve_map = {
        "student": "0 years",
        "entry": "0-1 years",
        "experienced": "1-3 years",
        "senior": "3-5 years",
        "expert": "5-7 years",
        "manager": "5-7 years",
        "director_or_above": "7-9 years",
    }

    # Find current level in job progression
    current_level_job = None
    current_level_index = -1

    if current_level == "student":
        salary_data = {
            "current_level": {
                "job_title": "",
                "level": "Student",
                "education": {
                    "major": major,
                    "university": university,
                },
                "salary_range": {
                    "salary_min": 0,
                    "salary_max": 0,
                    "salary_unit": salary_unit,
                },
            },
            "standard_level": [],
            "expert_level": [],
            "manager_level": [],
        }
        current_level_index = 0
    else:
        # Search across all progression arrays for current level
        all_progressions = [
            ("standard", standard_progression),
            ("manager", manager_progression),
            ("expert", expert_progression),
        ]

        for progression_type, progression in all_progressions:
            for i, level in enumerate(progression):
                if level["job_level"] == current_level:
                    current_level_job = level
                    # For standard progression, use actual index
                    # For manager/expert, use -1 to indicate not in standard progression
                    current_level_index = i if progression_type == "standard" else -1
                    break
            if current_level_job:
                break

        if not current_level_job:
            # Default to first level if current level not found
            current_level_job = (
                standard_progression[0] if standard_progression else None
            )
            current_level_index = 0

        # Generate salary structure
        if current_level_job:
            salary_data = {
                "current_level": {
                    "job_title": current_level_job["job_title"],
                    "level": current_level_job["job_level"]
                    .replace("_", " ")
                    .capitalize(),
                    "salary_range": {
                        "salary_min": int(current_level_job["salary_min"]),
                        "salary_max": int(current_level_job["salary_max"]),
                        "salary_unit": salary_unit,
                    },
                },
                "standard_level": [],
                "expert_level": [],
                "manager_level": [],
            }
        else:
            # Fallback if no job found
            salary_data = {
                "current_level": {
                    "job_title": "",
                    "level": current_level.replace("_", " ").capitalize(),
                    "salary_range": {
                        "salary_min": 0,
                        "salary_max": 0,
                        "salary_unit": salary_unit,
                    },
                },
                "standard_level": [],
                "expert_level": [],
                "manager_level": [],
            }

    # Generate other levels
    # Only show standard progression if current level is in standard progression
    if current_level_index >= 0:  # current_level_index is -1 for manager/expert levels
        for i, level in enumerate(standard_progression):
            if (current_level == "student" and i >= current_level_index) or (
                current_level != "student" and i > current_level_index
            ):
                level_data = {
                    "job_title": level["job_title"],
                    "level": level["job_level"].replace("_", " ").capitalize(),
                    "salary_range": {
                        "salary_min": int(level["salary_min"]),
                        "salary_max": int(level["salary_max"]),
                        "salary_unit": salary_unit,
                    },
                    "time_to_achieve": time_to_achieve_map.get(
                        level["job_level"], "1-2 years"
                    ),
                }
                salary_data["standard_level"].append(level_data)

    # Get current job title to exclude from other progressions
    current_job_title = current_level_job["job_title"] if current_level_job else ""

    # Show expert progression: exclude if already expert, but allow if manager (cross-progression)
    for i, level in enumerate(expert_progression):
        # Skip if this job title is already the current level
        if level["job_title"] == current_job_title:
            continue

        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
            "time_to_achieve": time_to_achieve_map.get(level["job_level"], "1-2 years"),
        }
        salary_data["expert_level"].append(level_data)

    # Show manager progression: exclude if already manager, but allow if expert (cross-progression)
    for i, level in enumerate(manager_progression):
        # Skip if this job title is already the current level
        if level["job_title"] == current_job_title:
            continue

        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
            "time_to_achieve": time_to_achieve_map.get(level["job_level"], "1-2 years"),
        }
        salary_data["manager_level"].append(level_data)

    return salary_data
