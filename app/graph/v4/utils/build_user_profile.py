"""Utility functions for Graph v4."""

from typing import Any, Dict, Union


def build_user_profile(data: Union[Dict[str, Any], Any]) -> str:
    """
    Build standardized user profile string from input data.

    Args:
        data: Dictionary or TypedDict containing user profile fields

    Returns:
        Formatted user profile string
    """
    # Convert TypedDict to dict if needed
    if hasattr(data, "keys") and not isinstance(data, dict):
        data = dict(data)

    return f"""Ngành học: {data.get("major", "") or "Không có thông tin"}
Trường đại học: {data.get("university", "") or "Không có thông tin"}
Tính cách: {data.get("characteristics", "") or "Không có thông tin"}
Sở thích: {data.get("hobbies", "") or "Không có thông tin"}
K<PERSON> năng : {data.get("skills_gained", "") or "Không có thông tin"}
<PERSON><PERSON><PERSON> họ<PERSON> yêu thích: {data.get("favourite_subject", "") or "Không có thông tin"}
<PERSON><PERSON> nghiệm làm việc: {data.get("experience", "") or "Không có thông tin"}
Trình độ ngoại ngữ: {data.get("language_level", "") or "Không có thông tin"}"""
