import asyncio
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv

from app.exceptions.custom_exception import DatabaseError, LLMError
from app.graph.v4.models import BestCareerPaths, SuggestGroupFunction
from app.graph.v4.prompts import (
    SUGGEST_CAREER_PATH_PROMPT,
    SUGGEST_GROUP_FUNCTION_PROMPT,
)
from app.graph.v4.state import InputState
from app.graph.v4.utils import build_user_profile, calculate_matching_score
from app.services.llm import get_chat_openai
from app.services.neo4j import get_neo4j_service

# Constants
MIN_MATCHING_SCORE = 50
DEFAULT_NUMBER_OUTPUT = 8
MAX_NUMBER_OUTPUT = 20
DEFAULT_IT_GROUP = "Information Technology"
DEFAULT_PROJECT_MANAGER = "Project Manager"
DEFAULT_OPERATIONS_GROUP = "Operations"


load_dotenv()

llm = get_chat_openai(model="gpt-4o", temperature=0)


async def get_group_function(career_path: str) -> str:
    try:
        async with get_neo4j_service() as neo4j:
            query = "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function"
            results = await neo4j.execute_query(query, {"career_path": career_path})
            if results:
                return results[0]["group_function"]
            else:
                return ""
    except Exception as e:
        print(f"Error fetching group function for {career_path}: {e}")
        raise DatabaseError(f"Failed to fetch group function for {career_path}") from e


async def get_career_path_id(
    career_path_name: str, group_function: str
) -> Optional[str]:
    """Get career path ID from career path name and group function"""
    try:
        async with get_neo4j_service() as neo4j:
            query = """
                MATCH (c:CareerPath)
                WHERE c.name = $career_path_name AND c.group_function = $group_function
                RETURN c.id as career_path_id
            """
            results = await neo4j.execute_query(
                query,
                {
                    "career_path_name": career_path_name,
                    "group_function": group_function,
                },
            )
            if results:
                return results[0]["career_path_id"]
            else:
                return None
    except Exception as e:
        print(f"Error fetching career path ID for {career_path_name}: {e}")
        return None


async def get_salary_and_market_outlook(
    career_path_name: str, group_function: str, current_level: str
) -> Dict[str, Any]:
    """Get salary and market outlook for a career path based on user's current level"""
    # If student level, use entry level for salary
    query_level = "entry" if current_level == "student" else current_level

    async with get_neo4j_service() as neo4j:
        query = """
            MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
            WHERE c.name = $career_path
            AND r.level = $level
            AND c.group_function = $group_function
            RETURN COALESCE(r.salary_min, 0) as salary_min, COALESCE(r.salary_max, 0) as salary_max, j.market_outlook as market_outlook
            LIMIT 1
        """

        results = await neo4j.execute_query(
            query,
            {
                "career_path": career_path_name,
                "level": query_level,
                "group_function": group_function,
            },
        )

        if results:
            return {
                "salary": {
                    "salary_min": results[0]["salary_min"],
                    "salary_max": results[0]["salary_max"],
                    "salary_unit": "triệu VND/tháng",
                },
                "market_outlook": results[0]["market_outlook"],
            }
        else:
            # Fallback to experienced level if specific level not found
            results = await neo4j.execute_query(
                query,
                {
                    "career_path": career_path_name,
                    "level": "experienced",
                    "group_function": group_function,
                },
            )
            if results:
                return {
                    "salary": {
                        "salary_min": results[0]["salary_min"],
                        "salary_max": results[0]["salary_max"],
                        "salary_unit": "triệu VND/tháng",
                    },
                    "market_outlook": results[0]["market_outlook"],
                }
            else:
                return {
                    "salary": {
                        "salary_min": None,
                        "salary_max": None,
                        "salary_unit": "triệu VND/tháng",
                    },
                    "market_outlook": None,
                }


async def evaluate_career_paths_parallel(
    career_paths: List[str], user_profile: str
) -> List[Dict[str, Any]]:
    """Evaluate multiple career paths in parallel and return with matching scores"""
    # Create evaluation tasks for all career paths
    tasks = [
        calculate_matching_score(career_path, user_profile)
        for career_path in career_paths
    ]

    # Execute all evaluations in parallel
    matching_scores = await asyncio.gather(*tasks, return_exceptions=True)
    # Combine career paths with their matching scores
    career_paths_with_scores = []
    for i, career_path in enumerate(career_paths):
        result = matching_scores[i]
        if isinstance(result, Exception):
            # Handle exceptions by using default values
            score, current_level, generic_info = 0, "unknown", "Error in evaluation"
        else:
            score, current_level, generic_info = result
        career_path_with_score = {
            "career_path": career_path,
            "matching_score": score,
            "current_level": current_level,
            "generic_info": generic_info,
        }
        career_paths_with_scores.append(career_path_with_score)

    return career_paths_with_scores


async def validate_input(state: InputState) -> int:
    """Validate and sanitize number_output parameter"""
    number_output = state.get("number_output", DEFAULT_NUMBER_OUTPUT)
    if not isinstance(number_output, int) or number_output <= 0:
        number_output = DEFAULT_NUMBER_OUTPUT
    elif number_output > MAX_NUMBER_OUTPUT:  # Set reasonable upper limit
        number_output = MAX_NUMBER_OUTPUT
    return number_output


async def fetch_group_functions(state: InputState) -> List[str]:
    """Fetch group functions based on user profile using LLM"""
    try:
        suggest_group_function_chain = llm.with_structured_output(SuggestGroupFunction)
        prompt = SUGGEST_GROUP_FUNCTION_PROMPT.format(
            user_profile=f"Ngành học: {state['major']}\nKinh nghiệm làm việc: {state['experience']}"
        )
        response = await suggest_group_function_chain.ainvoke(prompt)
        if not isinstance(response, SuggestGroupFunction):
            raise LLMError("Invalid response from LLM")
    except Exception as e:
        print(f"Error fetching group functions: {e}")
        raise LLMError("Failed to fetch group functions") from e

    major_based_group = response.major_based_group
    experience_based_group = response.experience_based_group

    # Collect unique group functions to query
    group_functions = (
        major_based_group
        if isinstance(major_based_group, list)
        else [major_based_group]
    )
    if experience_based_group:
        # Handle both string and list types for experience_based_group
        experience_groups = (
            experience_based_group
            if isinstance(experience_based_group, list)
            else [experience_based_group]
        )
        # Add experience groups that aren't already in group_functions
        for group in experience_groups:
            if group not in group_functions:
                group_functions.append(group)

    return group_functions


async def query_career_paths(
    group_functions: List[str],
) -> tuple[List[Dict[str, str]], Dict[str, str]]:
    """Query career paths from Neo4j based on group functions"""
    async with get_neo4j_service() as db:
        # Optimize: Use single query with IN clause instead of N+1 queries
        all_group_functions = group_functions.copy()

        # Always include IT career paths if not already in the list
        if DEFAULT_IT_GROUP not in all_group_functions:
            all_group_functions.append(DEFAULT_IT_GROUP)

        # Single batched query to get all career paths for all group functions
        batch_query = f"""
            MATCH (c:CareerPath)
            WHERE c.group_function IN $group_functions
            RETURN DISTINCT
                c.name AS career_path,
                c.group_function AS group_function
            UNION
            RETURN "{DEFAULT_PROJECT_MANAGER}" AS career_path, "{DEFAULT_OPERATIONS_GROUP}" AS group_function
        """

        records = await db.execute_query(
            batch_query, {"group_functions": all_group_functions}
        )

        # Convert to the expected format and remove duplicates while preserving order
        seen = set()
        career_paths = []
        career_path_to_group = {}  # Map career path names to their group functions
        for record in records:
            career_path_name = record["career_path"]
            if career_path_name not in seen:
                seen.add(career_path_name)
                career_paths.append({"career_path": record["career_path"]})
                career_path_to_group[career_path_name] = record["group_function"]

    return career_paths, career_path_to_group


async def process_llm_response(
    user_profile: str, career_paths: List[Dict[str, str]], number_output: int
) -> List[str]:
    """Process LLM response to get final career paths"""
    try:
        suggest_career_path_chain = llm.with_structured_output(BestCareerPaths)
        career_paths_str = "\n".join(
            "- " + career_path["career_path"] for career_path in career_paths
        )
        # Create set for exact matching
        valid_career_paths = {
            career_path["career_path"] for career_path in career_paths
        }

        # Generate dynamic career paths format
        career_paths_format = "\n".join(
            [f"[Career path {i + 1}]" for i in range(number_output)]
        )

        prompt = SUGGEST_CAREER_PATH_PROMPT.format(
            user_profile=user_profile,
            career_paths=career_paths_str,
            number_output=number_output,
            career_paths_format=career_paths_format,
        )
        response = await suggest_career_path_chain.ainvoke(prompt)
        if not isinstance(response, BestCareerPaths):
            raise LLMError("Invalid response from LLM")
    except Exception as e:
        print(f"Error processing LLM response: {e}")
        raise LLMError("Failed to process LLM response") from e

    final_career_paths = [
        career_path
        for career_path in response.career_paths
        if career_path in valid_career_paths
    ]
    return final_career_paths


async def evaluate_and_filter_paths(
    final_career_paths: List[str], user_profile: str, number_output: int
) -> List[Dict[str, Any]]:
    """Evaluate career paths and filter top matches"""
    # Calculate evaluation scores for all career paths in parallel
    career_paths_with_scores = await evaluate_career_paths_parallel(
        final_career_paths, user_profile
    )

    # Sort career paths by matching score in descending order (highest score first)
    career_paths_with_scores.sort(key=lambda x: x["matching_score"], reverse=True)

    # Filter career paths with matching score >= MIN_MATCHING_SCORE
    filtered_career_paths = [
        cp
        for cp in career_paths_with_scores
        if cp["matching_score"] >= MIN_MATCHING_SCORE
    ]

    # Get the top N career paths based on number_output
    top_career_paths = filtered_career_paths[:number_output]
    return top_career_paths


async def fetch_additional_data(
    top_career_paths: List[Dict[str, Any]], career_path_to_group: Dict[str, str]
) -> List[Dict[str, Any]]:
    """Fetch salary and market outlook data for remaining career paths"""
    if not top_career_paths:
        return []

    # Get salary and market outlook data for remaining career paths in parallel
    remaining_salary_tasks = [
        get_salary_and_market_outlook(
            cp["career_path"],
            career_path_to_group.get(cp["career_path"], ""),
            cp["current_level"],
        )
        for cp in top_career_paths[1:]
    ]

    # Get career path IDs for remaining career paths in parallel
    remaining_id_tasks = [
        get_career_path_id(
            cp["career_path"],
            career_path_to_group.get(cp["career_path"], ""),
        )
        for cp in top_career_paths[1:]
    ]

    # Combine all tasks for more efficient execution
    all_remaining_tasks = remaining_salary_tasks + remaining_id_tasks
    all_results = await asyncio.gather(*all_remaining_tasks, return_exceptions=True)

    # Split results back into salary and ID data
    remaining_salary_data = all_results[: len(remaining_salary_tasks)]
    remaining_id_data = all_results[len(remaining_salary_tasks) :]

    remain_career_paths = []
    for i, cp in enumerate(top_career_paths[1:]):
        salary_market_data = remaining_salary_data[i]
        career_path_id = remaining_id_data[i]
        matching_score = cp["matching_score"]

        # Handle exceptions from async tasks
        if isinstance(salary_market_data, Exception):
            salary_market_data = None
        if isinstance(career_path_id, Exception):
            career_path_id = None

        career_path_data = {
            "career_path_id": career_path_id,
            "career_path_name": cp["career_path"],
            "group_function": career_path_to_group.get(cp["career_path"], ""),
            "matching_score": matching_score,
            "salary": salary_market_data.get("salary")
            if isinstance(salary_market_data, dict)
            else None,
            "market_outlook": salary_market_data.get("market_outlook")
            if isinstance(salary_market_data, dict)
            else None,
        }
        remain_career_paths.append(career_path_data)

    return remain_career_paths


async def suggest_career_path(state: InputState) -> Dict[str, Any]:
    """Main function to suggest career paths based on user profile"""
    # Validate input
    number_output = await validate_input(state)

    # Build user profile
    user_profile = build_user_profile(state)

    # Fetch group functions
    group_functions = await fetch_group_functions(state)

    # Query career paths
    career_paths, career_path_to_group = await query_career_paths(group_functions)

    # Process LLM response
    final_career_paths = await process_llm_response(
        user_profile, career_paths, number_output
    )

    # Evaluate and filter paths
    top_career_paths = await evaluate_and_filter_paths(
        final_career_paths, user_profile, number_output
    )

    # Prepare best match
    if top_career_paths:
        best_match_career_path = {
            "career_path": top_career_paths[0]["career_path"],
            "matching_score": top_career_paths[0]["matching_score"],
            "current_level": top_career_paths[0]["current_level"],
            "generic_info": top_career_paths[0]["generic_info"],
        }

        # Fetch additional data
        remain_career_paths = await fetch_additional_data(
            top_career_paths, career_path_to_group
        )
    else:
        # Fallback if no career paths found with score >= 50
        career_paths_with_scores = await evaluate_career_paths_parallel(
            final_career_paths, user_profile
        )
        best_match_career_path = {
            "career_path": career_paths_with_scores[0]["career_path"],
            "matching_score": career_paths_with_scores[0]["matching_score"],
            "current_level": career_paths_with_scores[0]["current_level"],
            "generic_info": career_paths_with_scores[0]["generic_info"],
        }
        remain_career_paths = []

    return {
        "best_match_career_path": best_match_career_path,
        "remain_career_paths": remain_career_paths,
    }
