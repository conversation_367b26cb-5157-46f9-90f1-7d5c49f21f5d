import hashlib
import random
from typing import Any, Dict, List

from app.graph.v4.state import Agent<PERSON><PERSON>
from app.graph.v4.utils import TOP_10_INDUSTRY_TREND, get_comprehensive_salary_data
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.llm import get_chat_openai
from app.services.neo4j import get_neo4j_service

llm = get_chat_openai(model="gpt-4o")

# Constants for job levels
JOB_LEVELS = {
    "entry": "entry",
    "experienced": "experienced",
    "senior": "senior",
    "manager": "manager",
    "director_or_above": "director_or_above",
    "expert": "expert",
}

PATH_TYPES = {
    "manager": "manager",
    "expert": "expert",
}


def _process_job_list(
    job_list: List[Dict[str, Any]], job_titles_seen: set
) -> List[Dict[str, Any]]:
    """Helper function to process and deduplicate job lists."""
    processed_jobs = []
    for job in job_list:
        if (
            job
            and job["job_title"] not in job_titles_seen
            and job["job_level"] != "director_or_above"
        ):
            processed_jobs.append(job)
            job_titles_seen.add(job["job_title"])
        else:
            processed_jobs.append(job)
    return processed_jobs


async def get_job_progression(
    career_path: str, group_function: str
) -> Dict[str, List[Dict[str, Any]]]:
    """Get comprehensive job progression data for a career path."""
    async with get_neo4j_service() as neo4j:
        path_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE c.name = $career_path AND c.group_function = $group_function
        RETURN
          COLLECT(CASE WHEN r.level = $entry AND r.path_type = $manager
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS entry_level_jobs,
          COLLECT(CASE WHEN r.level = $experienced AND r.path_type = $manager
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS experienced_level_jobs,
          COLLECT(CASE WHEN r.level = $senior AND r.path_type = $manager
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS senior_level_jobs,
          COLLECT(CASE WHEN r.level = $manager AND r.path_type = $manager
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS manager_level_jobs,
          COLLECT(CASE WHEN r.level = $director_or_above AND r.path_type = $manager
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS director_manager_jobs,
          COLLECT(CASE WHEN r.level = $expert AND r.path_type = $expert
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS expert_level_jobs,
          COLLECT(CASE WHEN r.level = $director_or_above AND r.path_type = $expert
            THEN {job_level: r.level, job_title: j.name_en, salary_min: COALESCE(r.salary_min, 0), salary_max: COALESCE(r.salary_max, 0), market_outlook: j.market_outlook}
            END) AS director_expert_jobs
        """

        try:
            path_records = await neo4j.execute_query(
                path_query,
                {
                    "career_path": career_path,
                    "group_function": group_function,
                    "entry": JOB_LEVELS["entry"],
                    "experienced": JOB_LEVELS["experienced"],
                    "senior": JOB_LEVELS["senior"],
                    "manager": JOB_LEVELS["manager"],
                    "director_or_above": JOB_LEVELS["director_or_above"],
                    "expert": JOB_LEVELS["expert"],
                },
            )
        except Exception as e:
            # Log error and return empty structure
            print(f"Error querying job progression: {e}")
            return {"standard": [], "expert": [], "manager": []}

        if path_records:
            standard_progression = []
            expert_progression = []
            manager_progression = []
            job_titles_seen = set()

            for record in path_records:
                # Process standard progression jobs
                standard_progression.extend(
                    _process_job_list(record["entry_level_jobs"], job_titles_seen)
                )
                standard_progression.extend(
                    _process_job_list(record["experienced_level_jobs"], job_titles_seen)
                )
                standard_progression.extend(
                    _process_job_list(record["senior_level_jobs"], job_titles_seen)
                )

                # Process manager progression jobs
                manager_progression.extend(
                    _process_job_list(record["manager_level_jobs"], job_titles_seen)
                )
                manager_progression.extend(
                    _process_job_list(record["director_manager_jobs"], job_titles_seen)
                )

                # Process expert progression jobs
                expert_progression.extend(
                    _process_job_list(record["expert_level_jobs"], job_titles_seen)
                )
                expert_progression.extend(
                    _process_job_list(record["director_expert_jobs"], job_titles_seen)
                )

            return {
                "standard": standard_progression,
                "expert": expert_progression,
                "manager": manager_progression,
            }
        else:
            return {"standard": [], "expert": [], "manager": []}


async def _handle_caching(cache_key: str, cache_data: Dict[str, Any]) -> Any:
    """Handle caching logic for career path processing."""
    try:
        cached_result = await cache_get(cache_key)
        if cached_result:
            return cached_result
    except Exception as e:
        print(f"Cache retrieval error: {e}")
    return None


async def _generate_statistics(
    cache_data: Dict[str, Any], matching_score: float, major: str, career_path_name: str
) -> Dict[str, Any]:
    """Generate deterministic statistics based on input data."""
    data_hash = hashlib.md5(str(cache_data).encode(), usedforsecurity=False).hexdigest()
    random_seed = int(data_hash[:8], 16)
    random.seed(random_seed)

    percentage = (
        float(random.randint(10, 20))
        if matching_score < 50
        else float(random.randint(20, 30))
    )
    return {
        "percentage": percentage,
        "major": major,
        "career_path": career_path_name,
    }


def _build_response(
    career_path: Dict[str, Any],
    comprehensive_salary_data: Dict[str, Any],
    statistics: Dict[str, Any],
) -> Dict[str, Any]:
    """Build the final response dictionary."""
    return {
        "career_path_id": career_path["career_path_id"],
        "career_path": career_path["career_path"],
        "group_function": career_path["group_function"],
        "job_function": career_path["job_function"],
        "matching_score": career_path["matching_score"],
        "generic_info": career_path["generic_info"],
        "top_industry_trend": 10
        if career_path["job_function"] in TOP_10_INDUSTRY_TREND
        else None,
        "description": career_path["description"],
        "skills_needed": career_path["skills_needed"],
        "salary": comprehensive_salary_data,
        "market_outlook": {"number_jobs": career_path["market_outlook"]},
        "statistics": statistics,
    }


async def process_career_path(
    career_path: Dict[str, Any],
    major: str,
    university: str,
    characteristics: str,
    hobbies: str,
    skills_gained: str,
    favourite_subject: str,
    experience: str,
    language_level: str,
) -> Dict[str, Any]:
    """Process career path data and return structured response."""
    # Extract values from career_path dict
    career_path_name = career_path["career_path"]
    matching_score = career_path["matching_score"]
    current_level = career_path["current_level"]
    group_function = career_path["group_function"]

    # Prepare cache data
    cache_data = {
        "career_path": career_path,
        "major": major,
        "university": university,
        "characteristics": characteristics,
        "hobbies": hobbies,
        "skills_gained": skills_gained,
        "favourite_subject": favourite_subject,
        "experience": experience,
        "language_level": language_level,
    }
    cache_key = generate_cache_key("career_path_process", cache_data)

    # Check cache
    cached_result = await _handle_caching(cache_key, cache_data)
    if cached_result:
        return cached_result

    # Get job progression and salary data
    try:
        job_progression = await get_job_progression(career_path_name, group_function)
        comprehensive_salary_data = get_comprehensive_salary_data(
            job_progression, current_level, major, university
        )
    except Exception as e:
        print(f"Error fetching job progression or salary data: {e}")
        comprehensive_salary_data = {}

    # Generate statistics
    statistics = await _generate_statistics(
        cache_data, matching_score, major, career_path_name
    )

    # Build response
    response = _build_response(career_path, comprehensive_salary_data, statistics)

    # Cache the result
    try:
        await cache_set(cache_key, response)
    except Exception as e:
        print(f"Cache set error: {e}")

    return response


async def postprocess_career_path(state: AgentState) -> Dict[str, Any]:
    """Postprocess the best matching career path and return structured data."""
    best_match_career_path = state["career_paths"][0]
    try:
        responses = await process_career_path(
            best_match_career_path,
            state["major"],
            state["university"],
            state["characteristics"],
            state["hobbies"],
            state["skills_gained"],
            state["favourite_subject"],
            state["experience"],
            state["language_level"],
        )
    except Exception as e:
        print(f"Error in postprocess_career_path: {e}")
        responses = {}

    return {
        "responses": responses,
        "remain_career_paths": state.get("remain_career_paths", []),
    }
