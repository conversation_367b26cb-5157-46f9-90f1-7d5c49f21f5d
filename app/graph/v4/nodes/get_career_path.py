from typing import Dict, List, Any

from app.graph.v4.state import Agent<PERSON><PERSON>
from app.graph.v4.utils import calculate_matching_score, build_user_profile
from app.services.neo4j import get_neo4j_service


async def fetch_career_path_data(
    neo4j: Any,
    career_path: str,
    group_function: str,
    group_function_map: Dict[str, str],
) -> Dict[str, Any]:
    """Helper function to fetch and process career path data with error handling and retries."""
    combined_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE c.name = $career_path
        AND r.level = "experienced"
        AND c.group_function = $group_function
        WITH c, j
        OPTIONAL MATCH (j)-[:UTILIZES]->(s:SkillV2)
        WITH c.id as career_path_id,
              c.description as description,
              c.function as job_function,
              j.market_outlook as market_outlook,
              s.type as skill_type,
              COLLECT(DISTINCT {name: s.name_en, description: s.description}) as skills
        RETURN career_path_id, description, job_function, market_outlook,
               skill_type, skills
        ORDER BY skill_type
    """

    query_results = await neo4j.execute_query(
        combined_query,
        {"career_path": career_path, "group_function": group_function},
    )

    if not query_results:
        # Try to get the correct group_function from our batched lookup first
        if career_path in group_function_map:
            correct_group_function = group_function_map[career_path]
            if correct_group_function != group_function:
                # Update group_function and retry the query
                group_function = correct_group_function
                query_results = await neo4j.execute_query(
                    combined_query,
                    {"career_path": career_path, "group_function": group_function},
                )

                if not query_results:
                    raise ValueError(
                        f"No experienced job title found for career path '{career_path}'"
                    )
            else:
                raise ValueError(
                    f"No experienced job title found for career path '{career_path}' with group function '{group_function}'"
                )
        else:
            # Fallback to individual query only if not in our batched results
            correct_group_function_records = await neo4j.execute_query(
                "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN DISTINCT c.group_function as group_function",
                {"career_path": career_path},
            )

            if correct_group_function_records:
                # Retry with the correct group_function
                correct_group_function = correct_group_function_records[0][
                    "group_function"
                ]
                if correct_group_function != group_function:
                    # Update group_function and retry the query
                    group_function = correct_group_function
                    query_results = await neo4j.execute_query(
                        combined_query,
                        {"career_path": career_path, "group_function": group_function},
                    )

                    if not query_results:
                        raise ValueError(
                            f"No experienced job title found for career path '{career_path}'"
                        )
                else:
                    raise ValueError(
                        f"No experienced job title found for career path '{career_path}' with group function '{group_function}'"
                    )
            else:
                raise ValueError(f"Career path '{career_path}' not found")

    # Process results - first record has career path info
    first_record = query_results[0]
    data = {
        "career_path": career_path,
        "group_function": group_function,
        "career_path_id": first_record["career_path_id"],
        "description": first_record["description"],
        "job_function": first_record["job_function"],
        "market_outlook": first_record["market_outlook"],
    }

    # Process skills from all records
    skills_by_type: Dict[str, List[Dict[str, str]]] = {}
    for record in query_results:
        if record["skill_type"] and record["skills"]:
            skills_by_type[record["skill_type"]] = record["skills"]

    data["skills_needed"] = {
        "technical_skills": skills_by_type.get("hard", [])[:4],
        "soft_skills": skills_by_type.get("soft", [])[:4],
    }

    if not any(data["skills_needed"].values()):
        # Skills might be empty if no skills are associated
        data["skills_needed"] = {"technical_skills": [], "soft_skills": []}

    return data


async def get_career_path(state: AgentState) -> Dict[str, List[Dict[str, Any]]]:
    initial_group_function: str = state.get("group_function", "")
    career_paths_data: List[Dict[str, Any]] = []

    if state.get("career_path"):
        user_profile = build_user_profile(state)
        matching_score, current_level, generic_info = await calculate_matching_score(
            state["career_path"], user_profile
        )
        career_paths_data = [
            {
                "career_path": state.get("career_path"),
                "matching_score": matching_score,
                "current_level": current_level,
                "generic_info": generic_info,
            }
        ]
    else:
        best_match = state.get("best_match_career_path")
        if best_match:
            career_paths_data = [best_match]

    career_paths: List[Dict[str, Any]] = []
    async with get_neo4j_service() as neo4j:
        # Pre-batch group function lookups for all career paths that need them
        career_paths_needing_group: List[str] = []
        group_function_map: Dict[str, str] = {}

        if not initial_group_function:
            # Collect all career path names that need group function lookup
            career_paths_needing_group = [
                cp.get("career_path", "")
                for cp in career_paths_data
                if cp.get("career_path")
            ]

            # Single batched query for all group functions
            if career_paths_needing_group:
                group_function_records = await neo4j.execute_query(
                    "MATCH (c:CareerPath) WHERE c.name IN $career_paths RETURN c.name as career_path, c.group_function as group_function",
                    {"career_paths": career_paths_needing_group},
                )
                group_function_map = {
                    record["career_path"]: record["group_function"]
                    for record in group_function_records
                    if "career_path" in record and "group_function" in record
                }

        # Process each career path (now with batched group function lookups)
        for career_path_data in career_paths_data:
            career_path: str = career_path_data.get("career_path", "")
            current_level: str = career_path_data.get("current_level", "")
            generic_info: str = career_path_data.get("generic_info", "")
            group_function: str = initial_group_function

            # Get group_function if not provided (now using batched lookup)
            if not group_function:
                group_function = group_function_map.get(career_path, "")
                if not group_function:
                    raise ValueError(f"Career path '{career_path}' not found")

            # Use helper function for data fetching and processing
            data = await fetch_career_path_data(
                neo4j, career_path, group_function, group_function_map
            )

            # Add additional fields from career_path_data
            data.update(
                {
                    "matching_score": career_path_data.get("matching_score", 0),
                    "current_level": current_level,
                    "generic_info": generic_info,
                }
            )

            career_paths.append(data)

    return {"career_paths": career_paths}
