"""Chain for determining current career level."""

from langchain_core.prompts import ChatPromptTemplate

from app.graph.v4.models import CurrentLevelSection
from app.graph.v4.prompts import CURRENT_LEVEL_PROMPT
from app.services.llm import get_chat_openai

llm = get_chat_openai(model="gpt-4o")

# Cached chain object to avoid recreation on every request
current_level_prompt = ChatPromptTemplate.from_template(template=CURRENT_LEVEL_PROMPT)
current_level_chain = current_level_prompt | llm.with_structured_output(
    CurrentLevelSection
)
