"""Chain for career path evaluation."""

from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.graph.v4.models import EvaluationSection
from app.graph.v4.prompts import EVALUATION_PROMPT
from app.services.llm import get_chat_openai

llm = get_chat_openai(model="gpt-4o")

# Cached chain object to avoid recreation on every request
evaluation_prompt = ChatPromptTemplate.from_template(template=EVALUATION_PROMPT)
evaluation_chain = evaluation_prompt | llm.with_structured_output(EvaluationSection)
