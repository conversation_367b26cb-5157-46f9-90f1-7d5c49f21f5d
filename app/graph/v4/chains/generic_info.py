from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.graph.v4.models import GenericInfoResponse
from app.graph.v4.prompts import HIGH_GENERIC_INFO_PROMPT, LOW_GENERIC_INFO_PROMPT
from app.services.llm import get_chat_openai

llm = get_chat_openai(model="gpt-4o")

# Cached chain objects to avoid recreation on every request
high_generic_info_template = ChatPromptTemplate.from_messages(
    messages=[
        ("system", HIGH_GENERIC_INFO_PROMPT),
        (
            "user",
            "<PERSON><PERSON> trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
        ),
    ],
    template_format="jinja2",
)
high_generic_info_chain = high_generic_info_template | llm.with_structured_output(
    GenericInfoResponse
)

low_generic_info_template = ChatPromptTemplate.from_messages(
    messages=[
        ("system", L<PERSON>_GENERIC_INFO_PROMPT),
        (
            "user",
            "<PERSON><PERSON> trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
        ),
    ],
    template_format="jinja2",
)
low_generic_info_chain = low_generic_info_template | llm.with_structured_output(
    GenericInfoResponse
)


async def get_generic_info(career_path: str, user_profile: str, matching_score: int):
    generic_info_chain = (
        high_generic_info_chain if matching_score >= 50 else low_generic_info_chain
    )
    generic_info_input = {
        "matching_score": matching_score,
        "user_profile": user_profile,
        "career_path": career_path,
    }
    generic_result: GenericInfoResponse = await generic_info_chain.ainvoke(
        generic_info_input
    )
    return generic_result.generic_info
