from langchain_core.prompts import ChatPromptTemplate

from app.graph.v4.models.responses import DetailedInfoResponse
from app.graph.v4.prompts import HIGH_DETAILED_INFO_PROMPT, LOW_DETAILED_INFO_PROMPT
from app.services.langfuse import langfuse_handler
from app.services.llm import get_chat_openai

llm = get_chat_openai(model="gpt-4o")

# Cached chain objects to avoid recreation on every request
high_detailed_info_template = ChatPromptTemplate.from_messages(
    messages=[
        ("system", HIGH_DETAILED_INFO_PROMPT),
        (
            "user",
            "<PERSON><PERSON> trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
        ),
    ],
    template_format="jinja2",
)
high_detailed_info_chain = high_detailed_info_template | llm.with_structured_output(
    DetailedInfoResponse
)

low_detailed_info_template = ChatPromptTemplate.from_messages(
    messages=[
        ("system", LOW_DETAILED_INFO_PROMPT),
        (
            "user",
            "<PERSON><PERSON> trình nghề nghiệp: {{career_path}}\nĐiểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}",
        ),
    ],
    template_format="jinja2",
)
low_detailed_info_chain = low_detailed_info_template | llm.with_structured_output(
    DetailedInfoResponse
)


async def get_detailed_info(career_path: str, user_profile: str, matching_score: int):
    detailed_info_chain = (
        high_detailed_info_chain if matching_score >= 50 else low_detailed_info_chain
    )
    detailed_info_input = {
        "matching_score": matching_score,
        "user_profile": user_profile,
        "career_path": career_path,
    }
    detailed_result: DetailedInfoResponse = await detailed_info_chain.ainvoke(
        detailed_info_input,
        config={
            "callbacks": [langfuse_handler],
            "run_name": "Detailed Info V4",
        },
    )
    return detailed_result.detailed_info
