"""Chain for experience analysis."""

from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.graph.v4.models import ExperienceSection
from app.graph.v4.prompts import EXPERIENCE_ANALYSIS_PROMPT
from app.services.llm import get_chat_openai

llm = get_chat_openai(model="gpt-4o")

# Cached chain object to avoid recreation on every request
experience_analysis_prompt = ChatPromptTemplate.from_template(
    template=EXPERIENCE_ANALYSIS_PROMPT
)
experience_analysis_chain = experience_analysis_prompt | llm.with_structured_output(
    ExperienceSection
)
