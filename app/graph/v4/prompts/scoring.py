"""Prompts for scoring and assessment responses."""

HIGH_GENERIC_INFO_PROMPT = """Generate personalized career path matching assessment in Vietnamese.

## Format:
- JSON with field "generic_info"
- Start with # headline
- Detail background and qualifications
- Highlight strengths

## Requirements:
- Emphasize excellent foundation using "tuyệt vời"
- Reference specific education/university
- Mention advantageous skills/interests
- Explain strong foundation for role
- Encouraging tone
- Maximum 50 words

## Example response format:

```json
{
  "generic_info": "# Bạn có nền tảng tốt cho vị trí Product Manager\n\nVới sự kết hợp giữa nền tảng kinh doanh, kỹ năng phân tích và đam mê công nghệ, bạn đã sẵn sàng để phát triển thành một Product Manager thành công!"
}
```

Always maintain this structure regardless of the specific career path or candidate profile. The response should be immediately actionable and help the candidate understand their suitability for the position."""

LOW_GENERIC_INFO_PROMPT = """Generate constructive career path matching assessment for low scores in Vietnamese.

## Format:
- JSON with field "generic_info"
- Start with # headline
- Honest but supportive evaluation
- Suggest alternative roles
- Include development path

## Requirements:
- Constructive, never discouraging
- Acknowledge gaps without negativity
- Suggest suitable alternatives
- Provide realistic development path
- Show path is difficult but possible
- Maximum 50 words

## Example response format:

```json
{
  "generic_info": "# Bạn có thể chưa thực sự phù hợp với vị trí Product Manager lúc này, cân nhắc thêm nha\n\nHãy cân nhắc các vị trí như Lập trình viên phần mềm, Kỹ thuật viên mạng hoặc Chuyên viên hỗ trợ kỹ thuật, nơi chuyên ngành Công nghệ thông tin của bạn được sử dụng hiệu quả hơn. Tuy nhiên, điểm số phù hợp chỉ là tham khảo. Nếu bạn thực sự đam mê quản lý sản phẩm, hãy bắt đầu trang bị kiến thức và kỹ năng cần thiết qua các khóa đào tạo, chứng chỉ về quản lý sản phẩm và thực hành các dự án thực tế"
}
```

Always maintain this structure regardless of the specific career path or candidate profile. The response should help the candidate understand why there's a lower match while providing constructive guidance on potential next steps and realistic alternatives."""

"""Prompts for scoring and assessment responses."""

HIGH_DETAILED_INFO_PROMPT = """Generate detailed personalized career path matching assessment in Vietnamese.

## Format:
- JSON with field "detailed_info"
- Start with compatibility percentage (e.g., "Độ phù hợp giữa bạn và [role] là **X%**")
- Section "Bạn phù hợp với hướng đi này vì:" with bullet points
- Section "Bạn sẽ cần bổ sung thêm:" with bullet points
- Section "Next steps:" with short-term and long-term plans
- End with "Top 3 công ty tại Việt Nam nè" and company list

## Requirements:
- Reference specific education/university background
- Detailed skills/interests analysis with concrete examples
- Comprehensive role suitability explanation with specific reasons
- Actionable development roadmap with timeline
- Encouraging and constructive tone
- Include practical application advice for job searching

## Example response format:

```json
{
  "detailed_info": "Độ phù hợp giữa bạn và Product Manager là **78%**\n\n**Bạn phù hợp với hướng đi này vì:**\n- Nền tảng Quản trị Kinh doanh tại Đại học Ngoại thương cung cấp kiến thức về chiến lược và phân tích thị trường cần thiết cho Product Manager\n- Kỹ năng phân tích dữ liệu và tư duy logic từ kinh nghiệm thực tập giúp đưa ra quyết định sản phẩm dựa trên insights\n- Sở thích về công nghệ và theo dõi trends giúp nắm bắt nhu cầu thị trường và đổi mới sản phẩm\n\n**Bạn sẽ cần bổ sung thêm:**\n- Hiểu biết sâu hơn về UX/UI design và nghiên cứu người dùng để thiết kế sản phẩm tốt hơn\n- Kỹ năng quản lý dự án Agile/Scrum để điều phối team phát triển hiệu quả\n- Kinh nghiệm thực tế trong việc launch và theo dõi metrics sản phẩm\n- Kỹ năng thuyết trình và storytelling để truyền đạt vision sản phẩm\n\n**Next steps:**\n- **Ngắn hạn:** Học các khóa về Product Management cơ bản, UX/UI fundamentals trên Coursera hoặc Udemy\n- **Dài hạn:** Tìm vị trí Product Analyst hoặc thực tập Product Management để tích lũy kinh nghiệm thực tế\n- **Ứng dụng khi tìm việc:** Tạo case study phân tích sản phẩm hiện có và đề xuất cải tiến để build portfolio. Target vào các startup tech Việt Nam nơi dễ tiếp cận role PM hơn\n\n**Top 3 công ty tại Việt Nam nè**\nVNG, Shopee, FPT Software"
}
```

Always maintain this structure regardless of the specific career path or candidate profile. The response should be immediately actionable and help the candidate understand their suitability for the position."""

LOW_DETAILED_INFO_PROMPT = """Generate detailed constructive career path matching assessment for low scores in Vietnamese.

## Format:
- JSON with field "detailed_info"
- Start with compatibility percentage (e.g., "Độ phù hợp giữa bạn và [role] là **X%**")
- Section "Hướng đi này sẽ hơi khó khăn đó:" with realistic gap analysis
- Section "Bạn sẽ cần bổ sung thêm:" with detailed skill/experience requirements
- Section "Bạn có thể cân nhắc thêm các hướng đi sau:" with alternative role suggestions

## Requirements:
- Constructive and supportive, never discouraging
- Honest but encouraging assessment of current gaps
- Multiple specific alternative role suggestions with explanations
- Comprehensive development plan with actionable steps
- Include timeline and practical roadmap for skill acquisition
- Acknowledge existing strengths while addressing gaps
- Supportive but realistic tone about transition possibilities

## Example response format:

```json
{
  "detailed_info": "Độ phù hợp giữa bạn và Product Manager là **38%**\n\n**Hướng đi này sẽ hơi khó khăn đó:**\n- Product Manager đòi hỏi sự kết hợp giữa tư duy kinh doanh, kỹ năng kỹ thuật và hiểu biết về trải nghiệm người dùng - những mảng hiện tại chưa thực sự mạnh trong profile của bạn\n- Thiếu kinh nghiệm thực tế trong việc quản lý sản phẩm, làm việc với stakeholders và ra quyết định dựa trên data sẽ là rào cản lớn khi apply vào các vị trí PM\n- Tuy nhiên, nền tảng kỹ thuật của bạn là lợi thế lớn - nhiều PM thành công xuất phát từ background technical\n- Với roadmap rõ ràng và kiên trì, bạn hoàn toàn có thể chuyển sang PM trong 1-2 năm tới\n\n**Bạn sẽ cần bổ sung thêm:**\n- **Foundation:** Học business fundamentals, marketing cơ bản, và customer psychology để hiểu được business logic\n- **Skills:** Nắm vững các tools như Figma, analytics platforms, và project management software (Jira, Notion)\n- **Experience:** Tìm cơ hội làm side project hoặc freelance các dự án có element product để build portfolio\n- **Mindset:** Phát triển customer-centric thinking và khả năng đưa ra quyết định trong môi trường uncertainty cao\n- **Network:** Kết nối với PM community Việt Nam, tham gia các meetup và workshop để học hỏi kinh nghiệm\n\n**Bạn có thể cân nhắc thêm các hướng đi sau:**\n- **Software Engineer/Developer** - tận dụng nền tảng kỹ thuật mạnh, có thể transition sang Technical PM sau này\n- **Data Analyst/Business Analyst** - stepping stone tốt để học business logic và làm quen với product metrics\n- **QA Engineer** - gần gũi với product development process, dễ hiểu user experience và product quality\n- **Technical Support/Customer Success** - học cách hiểu customer pain points, foundation quan trọng cho PM"
}
```

Always maintain this structure regardless of the specific career path or candidate profile. The response should help the candidate understand why there's a lower match while providing constructive guidance on potential next steps and realistic alternatives."""
