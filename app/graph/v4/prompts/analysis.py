"""Prompts for career path analysis and evaluation."""

EVALUATION_PROMPT = """You are an AI assistant specialized in career matching. Your task is to evaluate how well a user's profile matches a specific career path based on given criteria. Please analyze the following information carefully:

<user_profile>
{user_profile}
</user_profile>

The career path to evaluate against is:
<career_path>
{career_path}
</career_path>

Before providing your evaluation, please think through the process carefully. Use the following <analysis> tags inside your thinking block to show your analysis:

<analysis>
1. Extract key information from the user's profile:
   - Education background
   - Personality traits
   - Interests
   - Skills
   - Favorite subjects
   - Work experience
   - Language proficiency

2. List out the requirements and characteristics of the specified career path.

3. Compare the user's profile with the career path requirements:
   For each of the following criteria, note how well the user's attributes match the career requirements:
   a. <PERSON><PERSON>nh học phù hợp với công việc
   b. Trường đại học danh tiếng về ngành học
   c. Tính cách
   d. Sở thích
   e. <PERSON><PERSON> năng phù hợp với công việc
   f. <PERSON>ô<PERSON> học yêu thích phù hợp với công việc
   g. <PERSON> nghi<PERSON> làm việc phù hợp với công việc
   h. <PERSON>r<PERSON><PERSON> độ ngoại ngữ
</analysis>

After your analysis, provide your evaluation using the following format:

<evaluation>
Criterion: [criterion_name]
Score: [0-10, or -1 if criterion is empty (Không có thông tin)]
</evaluation>

Please provide your evaluation for all criteria, following the format specified above. Your final output should consist only of the evaluation and should not duplicate or rehash any of the work you did in the analysis section."""

CURRENT_LEVEL_PROMPT = """You are an AI assistant specialized in analyzing career levels. Your task is to determine the current job level of a user by comparing job titles in their working experience with the target career path.

<user_profile>
{user_profile}
</user_profile>

<career_path>
{career_path}
</career_path>

Analyze the user's current job level by comparing job titles in their working experience:

**Job Title Comparison Analysis:**
1. Extract all job titles from the user's working experience
2. Compare each job title with the target career path
3. Determine if there's an exact match

**Job Title Matching Rules:**
- **Exact Match**: Same job title OR similar domain/function (e.g., Software Engineer, Frontend Engineer, Backend Engineer) → Use seniority level from title
- **Unrelated Match**: Different domain/function that doesn't match career path → Default to "entry" regardless of previous seniority

**Examples:**
- DevOps Engineer experience + Sales Engineer career path → "entry" (different domains)
- Junior Software Engineer experience + Software Engineer career path → "entry" (exact match, junior level)
- Senior Product Manager experience + Product Manager career path → "senior" (exact match, senior level)
- Marketing Manager experience + Software Engineer career path → "entry" (different domains)
- Frontend Engineer experience + Software Engineer career path → Use seniority level (same domain)
- Senior Backend Developer experience + Software Engineer career path → "senior" (same domain)

**Decision Logic:**
- If no working experience → "student"
- If job titles are from different domain/function → "entry"
- If job titles match career path (exact or same domain) → Use seniority level from title
- If multiple matches → Use most recent or highest level

**Available levels**: "student", "entry", "experienced", "senior", "expert", "manager", "director_or_above"

Return the most accurate single level based on job title comparison with career path."""

EXPERIENCE_ANALYSIS_PROMPT = """You are an AI assistant specialized in analyzing work experience relevance. Your task is to analyze the user's work experience and determine its relevance to the target career path.

<user_profile>
{user_profile}
</user_profile>

<career_path>
{career_path}
</career_path>

Analyze the user's work experience and provide:

1. **has_experience**: Return true if the user has work experience that is directly relevant to the target career path. This should be an exact or very close match between their job titles/roles and the career path. Return false if they have no relevant experience in this specific career path.

2. **experience_segment**: Classify the experience level:
   - "no_experience": No work experience OR experience in completely different field
   - "entry_level": Relevant experience with junior titles, 0-2 years in target field
   - "experienced_level": 2+ years relevant experience, Senior/lead titles, Independent contributor roles

3. **time_period**: Extract the specific time period mentioned in the experience text. Examples: "2 years", "6 months", "1 năm", "3 tháng", "no experience", "chưa có kinh nghiệm". If no specific time is mentioned but experience exists, estimate based on job titles and context.

Focus on experience that is directly relevant to the target career path. General work experience in unrelated fields should not count as relevant experience."""
