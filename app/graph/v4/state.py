from typing import List

from typing_extensions import TypedDict


class InputState(TypedDict):
    career_path: str
    major: str
    university: str
    skills_gained: str
    experience: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str
    number_output: int
    group_function: str


class AgentState(InputState):
    career_paths: List[dict]
    best_match_career_path: dict
    remain_career_paths: List[str]
    responses: List[dict]


class OutputState(TypedDict):
    responses: List[dict]
    remain_career_paths: List[str]
