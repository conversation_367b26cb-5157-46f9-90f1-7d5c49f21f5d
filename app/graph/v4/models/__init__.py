"""Models package for graph v4."""

from .enums import (
    Criterion,
    CriterionWeightsEntryLevel,
    CriterionWeightsExperienced,
    CriterionWeightsNoExperience,
    JobLevel,
    job_level_mapping,
)
from .responses import (
    BestCareerPaths,
    CurrentLevelSection,
    DetailedInfoResponse,
    Evaluation,
    EvaluationResult,
    EvaluationSection,
    ExperienceSection,
    GenericInfoResponse,
    SuggestGroupFunction,
)

__all__ = [
    "Criterion",
    "CriterionWeightsEntryLevel",
    "CriterionWeightsExperienced",
    "CriterionWeightsNoExperience",
    "JobLevel",
    "job_level_mapping",
    "BestCareerPaths",
    "CurrentLevelSection",
    "Evaluation",
    "EvaluationResult",
    "EvaluationSection",
    "ExperienceSection",
    "GenericInfoResponse",
    "DetailedInfoResponse",
    "SuggestGroupFunction",
]
