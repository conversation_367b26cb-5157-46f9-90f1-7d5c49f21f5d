from typing import List, Literal

from pydantic import BaseModel, Field

from .enums import Criterion, JobLevel


class Evaluation(BaseModel):
    criterion: Criterion
    score: int


class EvaluationResult(BaseModel):
    evaluation: List[Evaluation]
    current_level: JobLevel = Field(
        description="The current level of the user profile in the job title"
    )
    has_experience: bool = Field(
        description="Boolean indicating if user has relevant work experience in the target career path"
    )
    experience_segment: str = Field(
        description="Experience segment: 'no_experience', 'entry_level', or 'experienced_level'"
    )
    time_period: str = Field(
        description="Detected time period from experience text (e.g., '2 years', '6 months', 'no experience')"
    )


class EvaluationSection(BaseModel):
    evaluation: List[Evaluation] = Field(
        description="List of evaluations for each criterion with scores 0-10 or -1 for no information"
    )


class CurrentLevelSection(BaseModel):
    current_level: JobLevel = Field(
        description="The current level of the user profile in the career path"
    )


class ExperienceSection(BaseModel):
    has_experience: bool = Field(
        description="Boolean indicating if user has relevant work experience in the target career path"
    )
    experience_segment: str = Field(
        description="Experience segment: 'no_experience', 'entry_level', or 'experienced_level'"
    )
    time_period: str = Field(
        description="Detected time period from experience text (e.g., '2 years', '6 months', 'no experience')"
    )


class GenericInfoResponse(BaseModel):
    generic_info: str = Field(description="Short title/summary in markdown format")


class DetailedInfoResponse(BaseModel):
    detailed_info: str = Field(
        description="Full detailed assessment in markdown format with all sections"
    )


class SuggestGroupFunction(BaseModel):
    major_based_group: List[
        Literal[
            "Accounting",
            "Administrative",
            "Agriculture & Fishery",
            "Arts and Design",
            "Business Development",
            "Construction & Architecture",
            "Consulting",
            "Customer Success and Support",
            "Education",
            "Entrepreneurship",
            "Finance",
            "Government & NGO",
            "Healthcare Services",
            "Hospitality & Food Services",
            "Human Resources",
            "Information Technology",
            "Insurance",
            "Legal",
            "Engineering",
            "Marketing",
            "Military and Protective Services",
            "Operations",
            "Purchasing",
            "Quality Assurance",
            "Real Estate",
            "Research",
            "Retail & Consumer Products",
            "Sales",
            "Transportation",
        ]
    ]
    experience_based_group: (
        List[
            Literal[
                "Accounting",
                "Administrative",
                "Agriculture & Fishery",
                "Arts and Design",
                "Business Development",
                "Construction & Architecture",
                "Consulting",
                "Customer Success and Support",
                "Education",
                "Entrepreneurship",
                "Finance",
                "Government & NGO",
                "Healthcare Services",
                "Hospitality & Food Services",
                "Human Resources",
                "Information Technology",
                "Insurance",
                "Legal",
                "Engineering",
                "Marketing",
                "Military and Protective Services",
                "Operations",
                "Purchasing",
                "Quality Assurance",
                "Real Estate",
                "Research",
                "Retail & Consumer Products",
                "Sales",
                "Transportation",
            ]
        ]
        | None
    ) = None


class BestCareerPaths(BaseModel):
    career_paths: List[str]
