from enum import Enum


class Criterion(Enum):
    MAJOR = "Major"
    UNIVERSITY = "University"
    FAVOURITE_SUBJECT = "Favourite subject"
    SKILLS_GAINED = "Skills gained"
    EXPERIENCE = "Experience"
    HOBBIES = "Hobbies"
    CHARACTERISTICS = "Characteristics"
    LANGUAGE_LEVEL = "Language level"


class CriterionWeightsNoExperience(Enum):
    MAJOR = 35
    UNIVERSITY = 15
    SKILLS_GAINED = 20
    FAVOURITE_SUBJECT = 10
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 5
    LANGUAGE_LEVEL = 5


class CriterionWeightsEntryLevel(Enum):
    MAJOR = 20
    UNIVERSITY = 10
    SKILLS_GAINED = 20
    FAVOURITE_SUBJECT = 5
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 30
    LANGUAGE_LEVEL = 5


class CriterionWeightsExperienced(Enum):
    MAJOR = 15
    UNIVERSITY = 5
    SKILLS_GAINED = 25
    FAVOURITE_SUBJECT = 5
    HOBBIES = 5
    CHARACTERISTICS = 5
    EXPERIENCE = 35
    LANGUAGE_LEVEL = 5


job_level_mapping = {
    "Student": "student",
    "Entry": "entry",
    "Experienced": "experienced",
    "Senior": "senior",
    "Expert": "expert",
    "Manager": "manager",
    "Director or above": "director_or_above",
}

JobLevel = Enum("JobLevel", job_level_mapping)
