from langgraph.graph import END, START, StateGraph

from app.graph.v2.nodes.evaluate_career_path import evaluate_career_path
from app.graph.v2.nodes.postprocess_evaluation import postprocess_evaluation
from app.graph.v2.state import EvalInputState, EvalOutputState, EvalState

workflow = StateGraph(EvalState, input=EvalInputState, output=EvalOutputState)

workflow.add_node("evaluate_career_path", evaluate_career_path)
workflow.add_node("postprocess_evaluation", postprocess_evaluation)
workflow.add_edge(START, "evaluate_career_path")
workflow.add_edge("evaluate_career_path", "postprocess_evaluation")
workflow.add_edge("postprocess_evaluation", END)

graph = workflow.compile()
