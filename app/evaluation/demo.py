"""Demonstration script for the evaluation system."""

import asyncio
from datetime import datetime

from app.evaluation.evaluator import evaluate_suggestions
from app.evaluation.generator import generate_test_cases
from app.evaluation.metrics import calculate_metrics
from app.evaluation.models import (
    EvaluationRun,
    TestCaseGenerationRequest,
)
from app.evaluation.report import generate_evaluation_report
from app.evaluation.runner import run_evaluation


async def run_demo_evaluation():
    """Run a demonstration evaluation with a small number of test cases."""
    print("🚀 Starting Career Path Evaluation System Demo")
    print("=" * 60)

    # Step 1: Generate test cases
    print("\n1️⃣ Generating test cases...")
    request = TestCaseGenerationRequest(
        count=5,  # Small number for demo
        diversity_focus=["education", "experience"],
        include_edge_cases=True,
        specific_domains=["Information Technology", "Business Development"],
    )

    test_suite = await generate_test_cases(
        request,
        name="Demo Evaluation Suite",
        description="Demonstration of the evaluation system",
    )

    print(f"✅ Generated {len(test_suite.test_cases)} test cases")
    print(f"   Suite ID: {test_suite.id}")
    print(f"   Suite Name: {test_suite.name}")

    # Show test case summary
    print("\n📊 Test Case Summary:")
    education_counts = {}
    experience_counts = {}
    focus_counts = {}

    for tc in test_suite.test_cases:
        education_counts[tc.education_level.value] = (
            education_counts.get(tc.education_level.value, 0) + 1
        )
        experience_counts[tc.experience_level.value] = (
            experience_counts.get(tc.experience_level.value, 0) + 1
        )
        focus_counts[tc.focus_area] = focus_counts.get(tc.focus_area, 0) + 1

    print("   Education levels:")
    for level, count in education_counts.items():
        print(f"     {level}: {count}")

    print("   Experience levels:")
    for level, count in experience_counts.items():
        print(f"     {level}: {count}")

    print("   Focus areas:")
    for area, count in focus_counts.items():
        print(f"     {area}: {count}")

    # Create evaluation run
    evaluation_run = EvaluationRun(
        test_suite_id=test_suite.id,
        test_suite_name="Demo Evaluation Run",
        status="running",
    )

    try:
        # Step 2: Run test cases through graph v4
        print("\n2️⃣ Running test cases through career path system...")
        suggestion_results = await run_evaluation(test_suite)

        successful_suggestions = [r for r in suggestion_results if not r.error]
        failed_suggestions = [r for r in suggestion_results if r.error]

        print("✅ Career path suggestions completed:")
        print(f"   Successful: {len(successful_suggestions)}")
        print(f"   Failed: {len(failed_suggestions)}")

        if failed_suggestions:
            print("   Failed cases:")
            for result in failed_suggestions:
                print(f"     - {result.test_case_id}: {result.error}")

        # Show average response time
        if successful_suggestions:
            avg_time = sum(r.execution_time for r in successful_suggestions) / len(
                successful_suggestions
            )
            print(f"   Average response time: {avg_time:.2f} seconds")

        # Step 3: Evaluate suggestions using Gemini
        print("\n3️⃣ Evaluating suggestions with Gemini AI...")
        print("   (This may take a few minutes...)")

        evaluation_results = await evaluate_suggestions(
            test_suite.test_cases, suggestion_results
        )

        successful_evaluations = [
            r for r in evaluation_results if r.scores and r.overall_score > 0
        ]
        failed_evaluations = [
            r for r in evaluation_results if not r.scores or r.overall_score == 0
        ]

        print("✅ AI evaluation completed:")
        print(f"   Successful evaluations: {len(successful_evaluations)}")
        print(f"   Failed evaluations: {len(failed_evaluations)}")

        if successful_evaluations:
            # Show top-performing case
            best_result = max(successful_evaluations, key=lambda x: x.overall_score)
            print(
                f"   Best performance: Test case {best_result.test_case_id} ({best_result.overall_score:.1f}/100)"
            )

            # Show worst-performing case
            worst_result = min(successful_evaluations, key=lambda x: x.overall_score)
            print(
                f"   Worst performance: Test case {worst_result.test_case_id} ({worst_result.overall_score:.1f}/100)"
            )

        # Step 4: Calculate metrics
        print("\n4️⃣ Calculating metrics and bias analysis...")
        aggregate_metrics, bias_analysis = calculate_metrics(
            test_suite.test_cases, evaluation_results
        )

        # Update evaluation run
        evaluation_run.evaluation_results = evaluation_results
        evaluation_run.aggregate_metrics = aggregate_metrics
        evaluation_run.bias_analysis = bias_analysis
        evaluation_run.status = "completed"
        evaluation_run.completed_at = datetime.now()

        # Step 5: Display results
        print("\n5️⃣ Results Summary:")
        print("=" * 40)

        if aggregate_metrics.successful_evaluations > 0:
            print("📈 Overall Performance:")
            print(f"   Mean Score: {aggregate_metrics.overall_score_mean:.1f}/100")
            print(
                f"   Score Range: {aggregate_metrics.overall_score_min:.1f} - {aggregate_metrics.overall_score_max:.1f}"
            )
            print(f"   Standard Deviation: {aggregate_metrics.overall_score_std:.1f}")
            print(
                f"   Success Rate: {(aggregate_metrics.successful_evaluations / aggregate_metrics.total_test_cases) * 100:.1f}%"
            )

            # Quality assessment
            if aggregate_metrics.overall_score_mean >= 80:
                quality = "🟢 Excellent"
            elif aggregate_metrics.overall_score_mean >= 70:
                quality = "🟡 Good"
            elif aggregate_metrics.overall_score_mean >= 60:
                quality = "🟠 Satisfactory"
            else:
                quality = "🔴 Needs Improvement"

            print(f"   Quality Assessment: {quality}")

            # Metric breakdown
            print("\n📊 Metric Breakdown:")
            for metric, scores in aggregate_metrics.metric_scores.items():
                print(
                    f"   {metric.replace('_', ' ').title()}: {scores['mean']:.1f}/100"
                )

            # Performance metrics
            print("\n⚡ Performance Metrics:")
            print(
                f"   Average Suggestion Time: {aggregate_metrics.avg_suggestion_time:.2f}s"
            )
            print(
                f"   Average Evaluation Time: {aggregate_metrics.avg_evaluation_time:.2f}s"
            )
            print(
                f"   Total Execution Time: {aggregate_metrics.total_execution_time:.2f}s"
            )

        # Bias analysis
        print("\n⚖️ Bias Analysis:")
        if bias_analysis.bias_indicators:
            for indicator in bias_analysis.bias_indicators:
                if "bias detected" in indicator.lower():
                    print(f"   ⚠️  {indicator}")
                else:
                    print(f"   ✅ {indicator}")

        # Step 6: Generate report
        print("\n6️⃣ Generating evaluation report...")
        reports = generate_evaluation_report(
            evaluation_run, test_suite.test_cases, "demo_reports"
        )

        print("📄 Reports generated:")
        print(f"   Text report: {reports['text_report']}")
        print(f"   JSON report: {reports['json_report']}")

        print("\n✅ Demo evaluation completed successfully!")
        print(f"   Run ID: {evaluation_run.id}")
        print(
            f"   Duration: {(evaluation_run.completed_at - evaluation_run.started_at).total_seconds():.1f} seconds"
        )

        return evaluation_run

    except Exception as e:
        evaluation_run.status = "failed"
        evaluation_run.errors.append(str(e))
        evaluation_run.completed_at = datetime.now()
        print(f"\n❌ Demo evaluation failed: {str(e)}")
        raise e


async def run_quick_test():
    """Run a quick test with just one test case."""
    print("🧪 Running Quick Test...")

    # Generate a single test case
    request = TestCaseGenerationRequest(count=1, include_edge_cases=False)
    test_suite = await generate_test_cases(
        request, name="Quick Test", description="Single test case"
    )

    print(
        f"Generated test case: {test_suite.test_cases[0].major} from {test_suite.test_cases[0].university}"
    )

    # Run through system
    suggestion_results = await run_evaluation(test_suite)
    print(f"Suggestion completed in {suggestion_results[0].execution_time:.2f}s")

    if suggestion_results[0].error:
        print(f"❌ Suggestion failed: {suggestion_results[0].error}")
        return

    print(f"✅ Generated {len(suggestion_results[0].responses)} career suggestions")

    # Quick evaluation
    evaluation_results = await evaluate_suggestions(
        test_suite.test_cases, suggestion_results
    )

    if evaluation_results[0].scores:
        print(f"✅ AI evaluation score: {evaluation_results[0].overall_score:.1f}/100")
        print("Top metrics:")
        for score in sorted(
            evaluation_results[0].scores, key=lambda x: x.score, reverse=True
        )[:3]:
            print(f"  {score.metric_name}: {score.score:.1f}")
    else:
        print("❌ AI evaluation failed")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        asyncio.run(run_quick_test())
    else:
        asyncio.run(run_demo_evaluation())
