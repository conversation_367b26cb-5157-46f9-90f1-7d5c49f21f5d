"""Test case generator using Gemini AI."""

import asyncio
import random
from typing import List

from langchain_core.prompts import Chat<PERSON>romptT<PERSON>plate
from pydantic import BaseModel

from app.evaluation.models import (
    CareerStage,
    EducationLevel,
    ExperienceLevel,
    TestCase,
    TestCaseGenerationRequest,
    TestSuite,
)
from app.evaluation.gemini import get_chat_gemini


class GeneratedTestCase(BaseModel):
    """Generated test case from Gemini."""

    major: str
    university: str
    skills_gained: str
    experience: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str
    education_level: EducationLevel
    experience_level: ExperienceLevel
    career_stage: CareerStage
    focus_area: str


class TestCaseList(BaseModel):
    """List of generated test cases."""

    test_cases: List[GeneratedTestCase]


TEST_CASE_GENERATION_PROMPT = """
Generate diverse, realistic user profiles for testing a career path recommendation system.
Each profile should represent a unique individual with different backgrounds, experiences, and aspirations.

Focus on creating diversity across:
- Educational backgrounds (different majors, universities)
- Experience levels (from fresh graduates to career switchers)
- Skill sets (technical, soft skills, domain-specific)
- Personal characteristics and interests
- Geographic and cultural diversity (Vietnamese context)

{specific_instructions}

Generate {count} test cases with the following structure for each:
- major: Academic field of study
- university: University name (mix of Vietnamese and international)
- skills_gained: Specific skills acquired through education/experience
- experience: Work experience description (can be "No work experience" for fresh graduates)
- language_level: Language proficiency (Vietnamese is native, English level varies)
- hobbies: Personal interests and hobbies
- characteristics: Personality traits and working style
- favourite_subject: Favorite academic subject or area of interest
- education_level: One of {education_levels}
- experience_level: One of {experience_levels}
- career_stage: One of {career_stages}
- focus_area: Primary domain/industry focus

Ensure profiles are realistic and represent actual career scenarios someone might face.
Include both traditional career paths and modern emerging fields.
"""

EDGE_CASE_PROMPT = """
Include challenging edge cases such as:
- Career switchers from unrelated fields
- Individuals with non-traditional educational backgrounds
- People with gaps in employment
- Those with mixed skill sets spanning multiple domains
- International students or professionals
- People with unique combinations of interests and skills
"""

DOMAIN_SPECIFIC_PROMPT = """
Focus on these specific domains: {domains}
Ensure good representation across all specified domains while maintaining diversity.
"""


class TestCaseGenerator:
    """Generates test cases using Gemini AI."""

    def __init__(self):
        self.llm = get_chat_gemini(model="gemini-2.5-flash", temperature=0.8)
        self.prompt_template = ChatPromptTemplate.from_template(
            TEST_CASE_GENERATION_PROMPT
        )

    async def generate_test_suite(
        self,
        request: TestCaseGenerationRequest,
        name: str = None,
        description: str = None,
    ) -> TestSuite:
        """Generate a complete test suite based on the request."""
        if name is None:
            name = f"Generated Test Suite - {request.count} cases"

        if description is None:
            description = (
                f"Auto-generated test suite with {request.count} diverse user profiles"
            )

        # Generate test cases in batches to avoid overwhelming the LLM
        batch_size = min(10, request.count)
        all_test_cases = []

        for i in range(0, request.count, batch_size):
            batch_count = min(batch_size, request.count - i)
            batch_cases = await self._generate_batch(request, batch_count)
            all_test_cases.extend(batch_cases)

            # Small delay to avoid rate limiting
            if i + batch_size < request.count:
                await asyncio.sleep(1)

        return TestSuite(name=name, description=description, test_cases=all_test_cases)

    async def _generate_batch(
        self, request: TestCaseGenerationRequest, count: int
    ) -> List[TestCase]:
        """Generate a batch of test cases."""
        specific_instructions = []

        if request.include_edge_cases:
            specific_instructions.append(EDGE_CASE_PROMPT)

        if request.specific_domains:
            specific_instructions.append(
                DOMAIN_SPECIFIC_PROMPT.format(
                    domains=", ".join(request.specific_domains)
                )
            )

        prompt_input = {
            "count": count,
            "specific_instructions": "\n".join(specific_instructions),
            "education_levels": [level.value for level in EducationLevel],
            "experience_levels": [level.value for level in ExperienceLevel],
            "career_stages": [stage.value for stage in CareerStage],
        }

        chain = self.prompt_template | self.llm.with_structured_output(TestCaseList)

        try:
            result = await chain.ainvoke(prompt_input)
            return [self._convert_to_test_case(case) for case in result.test_cases]
        except Exception as e:
            # Fallback to generating individual cases if batch fails
            print(
                f"Batch generation failed, falling back to individual generation: {e}"
            )
            return await self._generate_individual_cases(count)

    def _convert_to_test_case(self, generated_case: GeneratedTestCase) -> TestCase:
        """Convert generated case to TestCase model."""
        return TestCase(
            major=generated_case.major,
            university=generated_case.university,
            skills_gained=generated_case.skills_gained,
            experience=generated_case.experience,
            language_level=generated_case.language_level,
            hobbies=generated_case.hobbies,
            characteristics=generated_case.characteristics,
            favourite_subject=generated_case.favourite_subject,
            education_level=generated_case.education_level,
            experience_level=generated_case.experience_level,
            career_stage=generated_case.career_stage,
            focus_area=generated_case.focus_area,
            number_output=random.randint(
                5, 12
            ),  # Vary the number of suggestions requested
        )

    async def _generate_individual_cases(self, count: int) -> List[TestCase]:
        """Fallback method to generate individual test cases."""
        test_cases = []

        for _ in range(count):
            try:
                # Generate a single test case
                prompt = """
                Generate 1 realistic user profile for testing a career path recommendation system.

                Make it diverse and unique with realistic details.
                Include all required fields for a complete profile.
                """

                chain = self.prompt_template | self.llm.with_structured_output(
                    TestCaseList
                )
                result = await chain.ainvoke(
                    {
                        "count": 1,
                        "specific_instructions": prompt,
                        "education_levels": [level.value for level in EducationLevel],
                        "experience_levels": [level.value for level in ExperienceLevel],
                        "career_stages": [stage.value for stage in CareerStage],
                    }
                )

                if result.test_cases:
                    test_cases.append(self._convert_to_test_case(result.test_cases[0]))

            except Exception as e:
                print(f"Failed to generate individual test case: {e}")
                # Add a fallback hardcoded case if generation fails
                test_cases.append(self._create_fallback_case())

        return test_cases

    def _create_fallback_case(self) -> TestCase:
        """Create a fallback test case if generation fails."""
        fallback_cases = [
            {
                "major": "Computer Science",
                "university": "Vietnam National University",
                "skills_gained": "Python, JavaScript, Database Design",
                "experience": "6 months internship at tech startup",
                "language_level": "Advanced English",
                "hobbies": "Programming, Gaming, Reading tech blogs",
                "characteristics": "Analytical, Problem-solver, Team player",
                "favourite_subject": "Data Structures and Algorithms",
                "education_level": EducationLevel.BACHELOR,
                "experience_level": ExperienceLevel.ENTRY_LEVEL,
                "career_stage": CareerStage.FRESH_GRADUATE,
                "focus_area": "Information Technology",
            },
            {
                "major": "Business Administration",
                "university": "Ho Chi Minh City University of Economics",
                "skills_gained": "Financial Analysis, Project Management, Excel",
                "experience": "2 years as Marketing Coordinator",
                "language_level": "Intermediate English",
                "hobbies": "Photography, Traveling, Networking events",
                "characteristics": "Creative, Communicative, Results-driven",
                "favourite_subject": "Marketing Strategy",
                "education_level": EducationLevel.BACHELOR,
                "experience_level": ExperienceLevel.MID_LEVEL,
                "career_stage": CareerStage.EARLY_CAREER,
                "focus_area": "Business Development",
            },
        ]

        case_data = random.choice(fallback_cases)
        return TestCase(**case_data)


async def generate_test_cases(
    request: TestCaseGenerationRequest, name: str = None, description: str = None
) -> TestSuite:
    """Generate test cases based on the request."""
    generator = TestCaseGenerator()
    return await generator.generate_test_suite(request, name, description)
