"""Reporting system for evaluation results."""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from tabulate import tabulate

from app.evaluation.models import (
    AggregateMetrics,
    BiasAnalysis,
    EvaluationResult,
    EvaluationRun,
    TestCase,
)


class EvaluationReporter:
    """Generates comprehensive reports from evaluation results."""

    def generate_text_report(
        self, evaluation_run: EvaluationRun, test_cases: List[TestCase]
    ) -> str:
        """Generate a comprehensive text report."""
        report_lines = []

        # Header
        report_lines.extend(self._generate_header(evaluation_run))
        report_lines.append("")

        # Executive Summary
        report_lines.extend(self._generate_executive_summary(evaluation_run))
        report_lines.append("")

        # Overall Metrics
        if evaluation_run.aggregate_metrics:
            report_lines.extend(
                self._generate_metrics_section(evaluation_run.aggregate_metrics)
            )
            report_lines.append("")

        # Bias Analysis
        if evaluation_run.bias_analysis:
            report_lines.extend(
                self._generate_bias_section(evaluation_run.bias_analysis)
            )
            report_lines.append("")

        # Detailed Results
        report_lines.extend(
            self._generate_detailed_results(evaluation_run.evaluation_results)
        )
        report_lines.append("")

        # Test Case Analysis
        report_lines.extend(self._generate_test_case_analysis(test_cases))
        report_lines.append("")

        # Recommendations
        report_lines.extend(self._generate_recommendations(evaluation_run))

        return "\n".join(report_lines)

    def generate_json_report(self, evaluation_run: EvaluationRun) -> Dict:
        """Generate a JSON report with all data."""
        return {
            "run_id": evaluation_run.id,
            "test_suite_name": evaluation_run.test_suite_name,
            "started_at": evaluation_run.started_at.isoformat(),
            "completed_at": evaluation_run.completed_at.isoformat()
            if evaluation_run.completed_at
            else None,
            "status": evaluation_run.status,
            "aggregate_metrics": evaluation_run.aggregate_metrics.model_dump()
            if evaluation_run.aggregate_metrics
            else None,
            "bias_analysis": evaluation_run.bias_analysis.model_dump()
            if evaluation_run.bias_analysis
            else None,
            "evaluation_results": [
                result.model_dump() for result in evaluation_run.evaluation_results
            ],
            "errors": evaluation_run.errors,
            "generated_at": datetime.now().isoformat(),
        }

    def save_report(
        self,
        evaluation_run: EvaluationRun,
        test_cases: List[TestCase],
        output_dir: str = "evaluation_reports",
    ) -> Dict[str, str]:
        """Save both text and JSON reports to files."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"evaluation_{evaluation_run.id}_{timestamp}"

        # Generate and save text report
        text_report = self.generate_text_report(evaluation_run, test_cases)
        text_file = output_path / f"{base_name}.txt"
        text_file.write_text(text_report, encoding="utf-8")

        # Generate and save JSON report
        json_report = self.generate_json_report(evaluation_run)
        json_file = output_path / f"{base_name}.json"
        json_file.write_text(
            json.dumps(json_report, indent=2, ensure_ascii=False), encoding="utf-8"
        )

        return {
            "text_report": str(text_file),
            "json_report": str(json_file),
        }

    def _generate_header(self, evaluation_run: EvaluationRun) -> List[str]:
        """Generate report header."""
        duration = ""
        if evaluation_run.completed_at and evaluation_run.started_at:
            delta = evaluation_run.completed_at - evaluation_run.started_at
            duration = f" (Duration: {delta.total_seconds():.1f} seconds)"

        return [
            "=" * 80,
            "CAREER PATH EVALUATION REPORT",
            "=" * 80,
            f"Run ID: {evaluation_run.id}",
            f"Test Suite: {evaluation_run.test_suite_name}",
            f"Started: {evaluation_run.started_at.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Status: {evaluation_run.status.upper()}{duration}",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]

    def _generate_executive_summary(self, evaluation_run: EvaluationRun) -> List[str]:
        """Generate executive summary."""
        lines = ["EXECUTIVE SUMMARY", "-" * 20]

        if not evaluation_run.aggregate_metrics:
            lines.append("No metrics available - evaluation may have failed.")
            return lines

        metrics = evaluation_run.aggregate_metrics

        lines.extend(
            [
                f"• Total Test Cases: {metrics.total_test_cases}",
                f"• Successful Evaluations: {metrics.successful_evaluations}",
                f"• Failed Evaluations: {metrics.failed_evaluations}",
            ]
        )

        if metrics.successful_evaluations > 0:
            success_rate = (
                metrics.successful_evaluations / metrics.total_test_cases
            ) * 100
            lines.extend(
                [
                    f"• Success Rate: {success_rate:.1f}%",
                    f"• Overall Score (Mean): {metrics.overall_score_mean:.1f}/100",
                    f"• Score Range: {metrics.overall_score_min:.1f} - {metrics.overall_score_max:.1f}",
                    f"• Standard Deviation: {metrics.overall_score_std:.1f}",
                    f"• Average Response Time: {metrics.avg_suggestion_time:.2f}s",
                ]
            )

            # Quality assessment
            if metrics.overall_score_mean >= 80:
                quality = "Excellent"
            elif metrics.overall_score_mean >= 70:
                quality = "Good"
            elif metrics.overall_score_mean >= 60:
                quality = "Satisfactory"
            else:
                quality = "Needs Improvement"

            lines.append(f"• Overall Quality Assessment: {quality}")

        return lines

    def _generate_metrics_section(self, metrics: AggregateMetrics) -> List[str]:
        """Generate detailed metrics section."""
        lines = ["DETAILED METRICS", "-" * 20]

        # Performance metrics
        lines.extend(
            [
                "",
                "Performance Metrics:",
                f"  Average Suggestion Time: {metrics.avg_suggestion_time:.2f} seconds",
                f"  Average Evaluation Time: {metrics.avg_evaluation_time:.2f} seconds",
                f"  Total Execution Time: {metrics.total_execution_time:.2f} seconds",
            ]
        )

        # Score distribution
        lines.extend(
            [
                "",
                "Score Distribution:",
                f"  Mean: {metrics.overall_score_mean:.1f}",
                f"  Median: {metrics.overall_score_median:.1f}",
                f"  Standard Deviation: {metrics.overall_score_std:.1f}",
                f"  Minimum: {metrics.overall_score_min:.1f}",
                f"  Maximum: {metrics.overall_score_max:.1f}",
            ]
        )

        # Individual metric scores
        if metrics.metric_scores:
            lines.extend(["", "Individual Metric Performance:"])

            # Create table for better formatting
            table_data = []
            for metric_name, scores in metrics.metric_scores.items():
                table_data.append(
                    [
                        metric_name.replace("_", " ").title(),
                        f"{scores['mean']:.1f}",
                        f"{scores['std']:.1f}",
                        f"{scores['min']:.1f}",
                        f"{scores['max']:.1f}",
                        str(scores["count"]),
                    ]
                )

            table = tabulate(
                table_data,
                headers=["Metric", "Mean", "Std", "Min", "Max", "Count"],
                tablefmt="grid",
            )
            lines.extend(table.split("\n"))

        return lines

    def _generate_bias_section(self, bias_analysis: BiasAnalysis) -> List[str]:
        """Generate bias analysis section."""
        lines = ["BIAS ANALYSIS", "-" * 20]

        # Education level bias
        if bias_analysis.by_education_level:
            lines.extend(["", "Scores by Education Level:"])
            for level, score in sorted(bias_analysis.by_education_level.items()):
                lines.append(f"  {level.replace('_', ' ').title()}: {score:.1f}")

        # Experience level bias
        if bias_analysis.by_experience_level:
            lines.extend(["", "Scores by Experience Level:"])
            for level, score in sorted(bias_analysis.by_experience_level.items()):
                lines.append(f"  {level.replace('_', ' ').title()}: {score:.1f}")

        # Career stage bias
        if bias_analysis.by_career_stage:
            lines.extend(["", "Scores by Career Stage:"])
            for stage, score in sorted(bias_analysis.by_career_stage.items()):
                lines.append(f"  {stage.replace('_', ' ').title()}: {score:.1f}")

        # Focus area bias
        if bias_analysis.by_focus_area:
            lines.extend(["", "Scores by Focus Area:"])
            for area, score in sorted(bias_analysis.by_focus_area.items()):
                lines.append(f"  {area}: {score:.1f}")

        # Bias indicators
        if bias_analysis.bias_indicators:
            lines.extend(["", "Bias Indicators:"])
            for indicator in bias_analysis.bias_indicators:
                lines.append(f"  • {indicator}")

        return lines

    def _generate_detailed_results(
        self, evaluation_results: List[EvaluationResult]
    ) -> List[str]:
        """Generate detailed results section."""
        lines = ["TOP/BOTTOM PERFORMING TEST CASES", "-" * 40]

        if not evaluation_results:
            lines.append("No evaluation results available.")
            return lines

        # Sort by overall score
        successful_results = [
            r for r in evaluation_results if r.scores and r.overall_score > 0
        ]
        if not successful_results:
            lines.append("No successful evaluations to analyze.")
            return lines

        sorted_results = sorted(
            successful_results, key=lambda x: x.overall_score, reverse=True
        )

        # Top 3 performers
        lines.extend(["", "Top 3 Performing Test Cases:"])
        for i, result in enumerate(sorted_results[:3], 1):
            lines.append(f"{i}. Test Case {result.test_case_id}")
            lines.append(f"   Overall Score: {result.overall_score:.1f}")
            lines.append(
                f"   Execution Time: {result.suggestion_result.execution_time:.2f}s"
            )
            if result.scores:
                best_metric = max(result.scores, key=lambda x: x.score)
                lines.append(
                    f"   Best Metric: {best_metric.metric_name} ({best_metric.score:.1f})"
                )
            lines.append("")

        # Bottom 3 performers
        lines.extend(["Bottom 3 Performing Test Cases:"])
        for i, result in enumerate(sorted_results[-3:], 1):
            lines.append(f"{i}. Test Case {result.test_case_id}")
            lines.append(f"   Overall Score: {result.overall_score:.1f}")
            lines.append(
                f"   Execution Time: {result.suggestion_result.execution_time:.2f}s"
            )
            if result.scores:
                worst_metric = min(result.scores, key=lambda x: x.score)
                lines.append(
                    f"   Worst Metric: {worst_metric.metric_name} ({worst_metric.score:.1f})"
                )
            lines.append("")

        return lines

    def _generate_test_case_analysis(self, test_cases: List[TestCase]) -> List[str]:
        """Generate test case diversity analysis."""
        lines = ["TEST CASE DIVERSITY ANALYSIS", "-" * 35]

        if not test_cases:
            lines.append("No test cases to analyze.")
            return lines

        # Count distributions
        education_counts = {}
        experience_counts = {}
        career_stage_counts = {}
        focus_area_counts = {}

        for tc in test_cases:
            education_counts[tc.education_level.value] = (
                education_counts.get(tc.education_level.value, 0) + 1
            )
            experience_counts[tc.experience_level.value] = (
                experience_counts.get(tc.experience_level.value, 0) + 1
            )
            career_stage_counts[tc.career_stage.value] = (
                career_stage_counts.get(tc.career_stage.value, 0) + 1
            )
            focus_area_counts[tc.focus_area] = (
                focus_area_counts.get(tc.focus_area, 0) + 1
            )

        lines.extend(
            [
                f"Total Test Cases: {len(test_cases)}",
                "",
                "Education Level Distribution:",
            ]
        )
        for level, count in sorted(education_counts.items()):
            percentage = (count / len(test_cases)) * 100
            lines.append(
                f"  {level.replace('_', ' ').title()}: {count} ({percentage:.1f}%)"
            )

        lines.extend(["", "Experience Level Distribution:"])
        for level, count in sorted(experience_counts.items()):
            percentage = (count / len(test_cases)) * 100
            lines.append(
                f"  {level.replace('_', ' ').title()}: {count} ({percentage:.1f}%)"
            )

        lines.extend(["", "Career Stage Distribution:"])
        for stage, count in sorted(career_stage_counts.items()):
            percentage = (count / len(test_cases)) * 100
            lines.append(
                f"  {stage.replace('_', ' ').title()}: {count} ({percentage:.1f}%)"
            )

        return lines

    def _generate_recommendations(self, evaluation_run: EvaluationRun) -> List[str]:
        """Generate recommendations based on results."""
        lines = ["RECOMMENDATIONS", "-" * 20]

        if not evaluation_run.aggregate_metrics:
            lines.append("Unable to generate recommendations due to lack of metrics.")
            return lines

        metrics = evaluation_run.aggregate_metrics

        # Performance recommendations
        if metrics.overall_score_mean < 60:
            lines.append(
                "🔴 CRITICAL: Overall performance is below acceptable threshold (60/100)"
            )
            lines.append("   • Review career path matching algorithms")
            lines.append("   • Improve user profile analysis")
            lines.append("   • Enhance recommendation personalization")
        elif metrics.overall_score_mean < 70:
            lines.append("🟡 WARNING: Performance needs improvement")
            lines.append("   • Focus on weak metrics (see detailed metrics above)")
            lines.append("   • Improve recommendation diversity")
        else:
            lines.append("✅ Good overall performance")
            lines.append("   • Continue monitoring for consistency")

        # Response time recommendations
        if metrics.avg_suggestion_time > 10:
            lines.append("")
            lines.append("🔴 Performance Issue: Response time is too slow")
            lines.append("   • Optimize database queries")
            lines.append("   • Consider caching strategies")
            lines.append("   • Review LLM call efficiency")

        # Bias recommendations
        if (
            evaluation_run.bias_analysis
            and evaluation_run.bias_analysis.bias_indicators
        ):
            bias_detected = any(
                "bias detected" in indicator.lower()
                for indicator in evaluation_run.bias_analysis.bias_indicators
            )
            if bias_detected:
                lines.append("")
                lines.append("⚠️  Bias Detection: Potential bias found")
                lines.append("   • Review training data for underrepresented groups")
                lines.append("   • Ensure fair representation across all categories")
                lines.append("   • Consider bias mitigation strategies")

        # Specific metric recommendations
        if metrics.metric_scores:
            weak_metrics = [
                name
                for name, scores in metrics.metric_scores.items()
                if scores["mean"] < 60
            ]
            if weak_metrics:
                lines.append("")
                lines.append("📊 Weak Metrics Need Attention:")
                for metric in weak_metrics:
                    lines.append(
                        f"   • {metric.replace('_', ' ').title()}: {metrics.metric_scores[metric]['mean']:.1f}/100"
                    )

        return lines


def generate_evaluation_report(
    evaluation_run: EvaluationRun,
    test_cases: List[TestCase],
    output_dir: Optional[str] = None,
) -> Dict[str, str]:
    """Generate and save evaluation report."""
    reporter = EvaluationReporter()

    if output_dir:
        return reporter.save_report(evaluation_run, test_cases, output_dir)
    else:
        # Return in-memory reports
        return {
            "text_report": reporter.generate_text_report(evaluation_run, test_cases),
            "json_report": json.dumps(
                reporter.generate_json_report(evaluation_run), indent=2
            ),
        }
