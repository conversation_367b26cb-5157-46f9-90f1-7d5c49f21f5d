"""Metrics calculation and analysis for evaluation results."""

import statistics
from collections import defaultdict
from typing import Dict, List

from app.evaluation.models import (
    AggregateMetrics,
    BiasAnalysis,
    EvaluationResult,
    TestCase,
)


class MetricsCalculator:
    """Calculates aggregate metrics and bias analysis from evaluation results."""

    def calculate_aggregate_metrics(
        self, evaluation_results: List[EvaluationResult]
    ) -> AggregateMetrics:
        """Calculate aggregate metrics from evaluation results."""
        if not evaluation_results:
            return self._empty_metrics()

        # Filter successful evaluations
        successful_results = [
            result
            for result in evaluation_results
            if result.scores and result.overall_score > 0
        ]
        failed_count = len(evaluation_results) - len(successful_results)

        if not successful_results:
            return self._empty_metrics_with_failures(
                len(evaluation_results), failed_count
            )

        # Calculate overall score statistics
        overall_scores = [result.overall_score for result in successful_results]

        overall_score_stats = {
            "mean": statistics.mean(overall_scores),
            "std": statistics.stdev(overall_scores) if len(overall_scores) > 1 else 0.0,
            "median": statistics.median(overall_scores),
            "min": min(overall_scores),
            "max": max(overall_scores),
        }

        # Calculate metric-specific statistics
        metric_scores = self._calculate_metric_scores(successful_results)

        # Calculate performance metrics
        suggestion_times = [
            result.suggestion_result.execution_time
            for result in successful_results
            if result.suggestion_result.execution_time
        ]
        evaluation_times = [result.evaluation_time for result in successful_results]

        avg_suggestion_time = (
            statistics.mean(suggestion_times) if suggestion_times else 0.0
        )
        avg_evaluation_time = (
            statistics.mean(evaluation_times) if evaluation_times else 0.0
        )
        total_execution_time = sum(suggestion_times) + sum(evaluation_times)

        return AggregateMetrics(
            total_test_cases=len(evaluation_results),
            successful_evaluations=len(successful_results),
            failed_evaluations=failed_count,
            overall_score_mean=overall_score_stats["mean"],
            overall_score_std=overall_score_stats["std"],
            overall_score_median=overall_score_stats["median"],
            overall_score_min=overall_score_stats["min"],
            overall_score_max=overall_score_stats["max"],
            metric_scores=metric_scores,
            avg_suggestion_time=avg_suggestion_time,
            avg_evaluation_time=avg_evaluation_time,
            total_execution_time=total_execution_time,
        )

    def _calculate_metric_scores(
        self, results: List[EvaluationResult]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate statistics for each individual metric."""
        metric_data = defaultdict(list)

        # Collect scores by metric
        for result in results:
            for score in result.scores:
                metric_data[score.metric_name].append(score.score)

        # Calculate statistics for each metric
        metric_stats = {}
        for metric_name, scores in metric_data.items():
            if scores:
                metric_stats[metric_name] = {
                    "mean": statistics.mean(scores),
                    "std": statistics.stdev(scores) if len(scores) > 1 else 0.0,
                    "median": statistics.median(scores),
                    "min": min(scores),
                    "max": max(scores),
                    "count": len(scores),
                }
            else:
                metric_stats[metric_name] = {
                    "mean": 0.0,
                    "std": 0.0,
                    "median": 0.0,
                    "min": 0.0,
                    "max": 0.0,
                    "count": 0,
                }

        return metric_stats

    def analyze_bias(
        self, test_cases: List[TestCase], evaluation_results: List[EvaluationResult]
    ) -> BiasAnalysis:
        """Analyze potential biases in the evaluation results."""
        # Create mapping from test case ID to test case
        test_case_map = {tc.id: tc for tc in test_cases}

        # Filter successful results and match with test cases
        successful_results = [
            (test_case_map[result.test_case_id], result)
            for result in evaluation_results
            if result.test_case_id in test_case_map
            and result.scores
            and result.overall_score > 0
        ]

        if not successful_results:
            return BiasAnalysis(
                by_education_level={},
                by_experience_level={},
                by_career_stage={},
                by_focus_area={},
                bias_indicators=["Insufficient data for bias analysis"],
            )

        # Group scores by different attributes
        by_education = defaultdict(list)
        by_experience = defaultdict(list)
        by_career_stage = defaultdict(list)
        by_focus_area = defaultdict(list)

        for test_case, result in successful_results:
            score = result.overall_score
            by_education[test_case.education_level.value].append(score)
            by_experience[test_case.experience_level.value].append(score)
            by_career_stage[test_case.career_stage.value].append(score)
            by_focus_area[test_case.focus_area].append(score)

        # Calculate average scores for each group
        education_averages = {
            level: statistics.mean(scores)
            for level, scores in by_education.items()
            if scores
        }
        experience_averages = {
            level: statistics.mean(scores)
            for level, scores in by_experience.items()
            if scores
        }
        career_stage_averages = {
            stage: statistics.mean(scores)
            for stage, scores in by_career_stage.items()
            if scores
        }
        focus_area_averages = {
            area: statistics.mean(scores)
            for area, scores in by_focus_area.items()
            if scores
        }

        # Identify potential bias indicators
        bias_indicators = self._identify_bias_indicators(
            education_averages,
            experience_averages,
            career_stage_averages,
            focus_area_averages,
        )

        return BiasAnalysis(
            by_education_level=education_averages,
            by_experience_level=experience_averages,
            by_career_stage=career_stage_averages,
            by_focus_area=focus_area_averages,
            bias_indicators=bias_indicators,
        )

    def _identify_bias_indicators(
        self,
        education_avg: Dict[str, float],
        experience_avg: Dict[str, float],
        career_stage_avg: Dict[str, float],
        focus_area_avg: Dict[str, float],
    ) -> List[str]:
        """Identify potential bias indicators based on score differences."""
        indicators = []
        bias_threshold = 15.0  # Significant score difference threshold

        # Check education level bias
        if len(education_avg) > 1:
            max_edu = max(education_avg.values())
            min_edu = min(education_avg.values())
            if max_edu - min_edu > bias_threshold:
                max_level = max(education_avg, key=education_avg.get)
                min_level = min(education_avg, key=education_avg.get)
                indicators.append(
                    f"Education bias detected: {max_level} scores {max_edu:.1f} vs {min_level} scores {min_edu:.1f}"
                )

        # Check experience level bias
        if len(experience_avg) > 1:
            max_exp = max(experience_avg.values())
            min_exp = min(experience_avg.values())
            if max_exp - min_exp > bias_threshold:
                max_level = max(experience_avg, key=experience_avg.get)
                min_level = min(experience_avg, key=experience_avg.get)
                indicators.append(
                    f"Experience bias detected: {max_level} scores {max_exp:.1f} vs {min_level} scores {min_exp:.1f}"
                )

        # Check career stage bias
        if len(career_stage_avg) > 1:
            max_stage = max(career_stage_avg.values())
            min_stage = min(career_stage_avg.values())
            if max_stage - min_stage > bias_threshold:
                max_level = max(career_stage_avg, key=career_stage_avg.get)
                min_level = min(career_stage_avg, key=career_stage_avg.get)
                indicators.append(
                    f"Career stage bias detected: {max_level} scores {max_stage:.1f} vs {min_level} scores {min_stage:.1f}"
                )

        # Check focus area bias
        if len(focus_area_avg) > 1:
            max_area = max(focus_area_avg.values())
            min_area = min(focus_area_avg.values())
            if max_area - min_area > bias_threshold:
                max_level = max(focus_area_avg, key=focus_area_avg.get)
                min_level = min(focus_area_avg, key=focus_area_avg.get)
                indicators.append(
                    f"Focus area bias detected: {max_level} scores {max_area:.1f} vs {min_level} scores {min_area:.1f}"
                )

        if not indicators:
            indicators.append("No significant bias detected")

        return indicators

    def _empty_metrics(self) -> AggregateMetrics:
        """Return empty metrics for when no results are available."""
        return AggregateMetrics(
            total_test_cases=0,
            successful_evaluations=0,
            failed_evaluations=0,
            overall_score_mean=0.0,
            overall_score_std=0.0,
            overall_score_median=0.0,
            overall_score_min=0.0,
            overall_score_max=0.0,
            metric_scores={},
            avg_suggestion_time=0.0,
            avg_evaluation_time=0.0,
            total_execution_time=0.0,
        )

    def _empty_metrics_with_failures(self, total: int, failed: int) -> AggregateMetrics:
        """Return metrics with only failure information."""
        return AggregateMetrics(
            total_test_cases=total,
            successful_evaluations=0,
            failed_evaluations=failed,
            overall_score_mean=0.0,
            overall_score_std=0.0,
            overall_score_median=0.0,
            overall_score_min=0.0,
            overall_score_max=0.0,
            metric_scores={},
            avg_suggestion_time=0.0,
            avg_evaluation_time=0.0,
            total_execution_time=0.0,
        )


def calculate_metrics(
    test_cases: List[TestCase], evaluation_results: List[EvaluationResult]
) -> tuple[AggregateMetrics, BiasAnalysis]:
    """Calculate both aggregate metrics and bias analysis."""
    calculator = MetricsCalculator()

    aggregate_metrics = calculator.calculate_aggregate_metrics(evaluation_results)
    bias_analysis = calculator.analyze_bias(test_cases, evaluation_results)

    return aggregate_metrics, bias_analysis
