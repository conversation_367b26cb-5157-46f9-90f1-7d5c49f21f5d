"""Pydantic models for the evaluation system."""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from uuid import uuid4

from pydantic import BaseModel, Field


class EducationLevel(str, Enum):
    """Education level enum."""

    HIGH_SCHOOL = "high_school"
    ASSOCIATE = "associate"
    BACHELOR = "bachelor"
    MASTER = "master"
    DOCTORATE = "doctorate"


class ExperienceLevel(str, Enum):
    """Experience level enum."""

    NO_EXPERIENCE = "no_experience"
    ENTRY_LEVEL = "entry_level"
    MID_LEVEL = "mid_level"
    SENIOR_LEVEL = "senior_level"
    EXPERT_LEVEL = "expert_level"


class CareerStage(str, Enum):
    """Career stage enum."""

    FRESH_GRADUATE = "fresh_graduate"
    EARLY_CAREER = "early_career"
    MID_CAREER = "mid_career"
    SENIOR_CAREER = "senior_career"
    CAREER_SWITCHER = "career_switcher"


class TestCase(BaseModel):
    """Individual test case representing a user profile."""

    id: str = Field(default_factory=lambda: str(uuid4()))
    career_path: Optional[str] = None
    major: str = Field(description="Academic major/field of study")
    university: str = Field(description="University name")
    skills_gained: str = Field(description="Skills acquired")
    experience: str = Field(description="Work experience description")
    language_level: str = Field(description="Language proficiency level")
    hobbies: str = Field(description="Personal hobbies and interests")
    characteristics: str = Field(description="Personal characteristics")
    favourite_subject: str = Field(description="Favorite academic subject")
    number_output: int = Field(
        default=8, description="Number of career suggestions requested"
    )
    group_function: Optional[str] = None

    # Metadata for test categorization
    education_level: EducationLevel
    experience_level: ExperienceLevel
    career_stage: CareerStage
    focus_area: str = Field(description="Primary focus area or domain")


class TestSuite(BaseModel):
    """Collection of test cases."""

    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(description="Name of the test suite")
    description: str = Field(description="Description of the test suite")
    test_cases: List[TestCase]
    created_at: datetime = Field(default_factory=datetime.now)


class CareerSuggestionResult(BaseModel):
    """Result from running a test case through the career path system."""

    test_case_id: str
    responses: List[Dict] = Field(description="Career path suggestions from the system")
    remain_career_paths: List[str] = Field(description="Additional career paths")
    execution_time: float = Field(description="Time taken to generate suggestions")
    error: Optional[str] = None


class EvaluationScore(BaseModel):
    """Individual evaluation score for a metric."""

    metric_name: str
    score: float = Field(ge=0, le=100, description="Score from 0-100")
    explanation: str = Field(description="Detailed explanation of the score")


class EvaluationResult(BaseModel):
    """Evaluation result for a single test case."""

    test_case_id: str
    suggestion_result: CareerSuggestionResult
    scores: List[EvaluationScore]
    overall_score: float = Field(ge=0, le=100, description="Overall weighted score")
    evaluation_time: float = Field(description="Time taken for evaluation")
    evaluator_notes: str = Field(description="Additional notes from the evaluator")


class AggregateMetrics(BaseModel):
    """Aggregate metrics across all test cases."""

    total_test_cases: int
    successful_evaluations: int
    failed_evaluations: int

    # Metric statistics
    overall_score_mean: float
    overall_score_std: float
    overall_score_median: float
    overall_score_min: float
    overall_score_max: float

    # Individual metric scores
    metric_scores: Dict[str, Dict[str, float]] = Field(
        description="Statistics for each metric (mean, std, min, max)"
    )

    # Performance metrics
    avg_suggestion_time: float
    avg_evaluation_time: float
    total_execution_time: float


class BiasAnalysis(BaseModel):
    """Analysis of potential biases in the evaluation results."""

    by_education_level: Dict[str, float] = Field(
        description="Average scores by education level"
    )
    by_experience_level: Dict[str, float] = Field(
        description="Average scores by experience level"
    )
    by_career_stage: Dict[str, float] = Field(
        description="Average scores by career stage"
    )
    by_focus_area: Dict[str, float] = Field(description="Average scores by focus area")
    bias_indicators: List[str] = Field(
        description="Identified potential bias indicators"
    )


class EvaluationRun(BaseModel):
    """Complete evaluation run with all results and metrics."""

    id: str = Field(default_factory=lambda: str(uuid4()))
    test_suite_id: str
    test_suite_name: str
    started_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    status: str = Field(
        default="running", description="Status: running, completed, failed"
    )

    # Results
    evaluation_results: List[EvaluationResult] = Field(default_factory=list)
    aggregate_metrics: Optional[AggregateMetrics] = None
    bias_analysis: Optional[BiasAnalysis] = None

    # Configuration
    evaluation_config: Dict = Field(default_factory=dict)
    errors: List[str] = Field(default_factory=list)


class TestCaseGenerationRequest(BaseModel):
    """Request for generating test cases."""

    count: int = Field(ge=1, le=1000, description="Number of test cases to generate")
    diversity_focus: List[str] = Field(
        default_factory=list,
        description="Areas to focus on for diversity (e.g., 'education', 'experience', 'career_stage')",
    )
    include_edge_cases: bool = Field(
        default=True, description="Include edge cases and challenging scenarios"
    )
    specific_domains: List[str] = Field(
        default_factory=list,
        description="Specific domains to focus on (e.g., 'technology', 'healthcare', 'finance')",
    )


class EvaluationMetric(BaseModel):
    """Definition of an evaluation metric."""

    name: str
    description: str
    weight: float = Field(
        ge=0, le=1, description="Weight for calculating overall score"
    )
    min_score: float = Field(default=0)
    max_score: float = Field(default=100)
