"""Command-line interface for the evaluation system."""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

import click

from app.evaluation.evaluator import evaluate_suggestions
from app.evaluation.generator import generate_test_cases
from app.evaluation.metrics import calculate_metrics
from app.evaluation.models import (
    EvaluationRun,
    TestCaseGenerationRequest,
    TestSuite,
)
from app.evaluation.report import generate_evaluation_report
from app.evaluation.runner import run_evaluation


@click.group()
def cli():
    """Career Path Evaluation System CLI."""
    pass


@cli.command()
@click.option("--count", "-c", default=20, help="Number of test cases to generate")
@click.option("--output", "-o", help="Output file for test cases (JSON)")
@click.option("--domains", help="Comma-separated list of specific domains to focus on")
@click.option("--no-edge-cases", is_flag=True, help="Exclude edge cases")
@click.option("--name", help="Name for the test suite")
@click.option("--description", help="Description for the test suite")
def generate(count, output, domains, no_edge_cases, name, description):
    """Generate test cases for evaluation."""

    async def run_generation():
        # Parse domains
        domain_list = []
        if domains:
            domain_list = [d.strip() for d in domains.split(",")]

        # Create generation request
        request = TestCaseGenerationRequest(
            count=count,
            specific_domains=domain_list,
            include_edge_cases=not no_edge_cases,
        )

        click.echo(f"Generating {count} test cases...")
        if domain_list:
            click.echo(f"Focusing on domains: {', '.join(domain_list)}")

        # Generate test cases
        test_suite = await generate_test_cases(request, name, description)

        click.echo(f"✅ Generated {len(test_suite.test_cases)} test cases")

        # Save to file if specified
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(
                    test_suite.model_dump(),
                    f,
                    indent=2,
                    default=str,
                    ensure_ascii=False,
                )

            click.echo(f"💾 Saved test suite to {output_path}")
        else:
            # Print summary
            click.echo("\nTest Suite Summary:")
            click.echo(f"ID: {test_suite.id}")
            click.echo(f"Name: {test_suite.name}")
            click.echo(f"Description: {test_suite.description}")
            click.echo(f"Created: {test_suite.created_at}")

            # Show distribution
            education_counts = {}
            experience_counts = {}
            for tc in test_suite.test_cases:
                education_counts[tc.education_level.value] = (
                    education_counts.get(tc.education_level.value, 0) + 1
                )
                experience_counts[tc.experience_level.value] = (
                    experience_counts.get(tc.experience_level.value, 0) + 1
                )

            click.echo("\nEducation Level Distribution:")
            for level, level_count in sorted(education_counts.items()):
                click.echo(f"  {level}: {level_count}")

            click.echo("\nExperience Level Distribution:")
            for level, level_count in sorted(experience_counts.items()):
                click.echo(f"  {level}: {level_count}")

    # Run the async function
    try:
        asyncio.run(run_generation())
    except KeyboardInterrupt:
        click.echo("\n❌ Generation cancelled by user")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Generation failed: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option("--test-suite", "-t", help="Path to test suite JSON file")
@click.option("--count", "-c", help="Generate test cases on the fly (number of cases)")
@click.option(
    "--output-dir",
    "-o",
    default="evaluation_reports",
    help="Output directory for reports",
)
@click.option("--name", help="Name for the evaluation run")
@click.option("--domains", help="Comma-separated domains for generated test cases")
@click.option(
    "--no-edge-cases", is_flag=True, help="Exclude edge cases in generated test cases"
)
def evaluate(test_suite, count, output_dir, name, domains, no_edge_cases):
    """Run evaluation on test cases."""

    async def run_evaluation_async():
        # Load or generate test suite
        if test_suite:
            # Load from file
            test_suite_path = Path(test_suite)
            if not test_suite_path.exists():
                click.echo(f"❌ Test suite file not found: {test_suite_path}")
                sys.exit(1)

            with open(test_suite_path, "r", encoding="utf-8") as f:
                test_suite_data = json.load(f)

            test_suite_obj = TestSuite.model_validate(test_suite_data)
            click.echo(f"📂 Loaded test suite from {test_suite_path}")
            click.echo(f"   Name: {test_suite_obj.name}")
            click.echo(f"   Test cases: {len(test_suite_obj.test_cases)}")

        elif count:
            # Generate test cases
            domain_list = []
            if domains:
                domain_list = [d.strip() for d in domains.split(",")]

            request = TestCaseGenerationRequest(
                count=int(count),
                specific_domains=domain_list,
                include_edge_cases=not no_edge_cases,
            )

            click.echo(f"🎲 Generating {count} test cases...")
            test_suite_obj = await generate_test_cases(request)
            click.echo(f"✅ Generated {len(test_suite_obj.test_cases)} test cases")

        else:
            click.echo("❌ Either --test-suite or --count must be specified")
            sys.exit(1)

        # Create evaluation run
        run_name = (
            name or f"CLI Evaluation - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        evaluation_run = EvaluationRun(
            test_suite_id=test_suite_obj.id,
            test_suite_name=run_name,
            status="running",
        )

        click.echo(f"\n🚀 Starting evaluation run: {run_name}")
        click.echo(f"   Run ID: {evaluation_run.id}")

        try:
            # Step 1: Run test cases through graph v4
            click.echo("📊 Running test cases through career path system...")
            suggestion_results = await run_evaluation(test_suite_obj)

            successful_suggestions = [r for r in suggestion_results if not r.error]
            failed_suggestions = [r for r in suggestion_results if r.error]

            click.echo(f"   ✅ Successful: {len(successful_suggestions)}")
            click.echo(f"   ❌ Failed: {len(failed_suggestions)}")

            if failed_suggestions:
                click.echo("   Failed test cases:")
                for result in failed_suggestions[:5]:  # Show first 5 failures
                    click.echo(f"     - {result.test_case_id}: {result.error}")
                if len(failed_suggestions) > 5:
                    click.echo(f"     ... and {len(failed_suggestions) - 5} more")

            # Step 2: Evaluate suggestions using Gemini
            click.echo("\n🤖 Evaluating suggestions with Gemini AI...")
            evaluation_results = await evaluate_suggestions(
                test_suite_obj.test_cases, suggestion_results
            )

            successful_evaluations = [
                r for r in evaluation_results if r.scores and r.overall_score > 0
            ]
            failed_evaluations = [
                r for r in evaluation_results if not r.scores or r.overall_score == 0
            ]

            click.echo(f"   ✅ Successful evaluations: {len(successful_evaluations)}")
            click.echo(f"   ❌ Failed evaluations: {len(failed_evaluations)}")

            # Step 3: Calculate metrics
            click.echo("\n📈 Calculating metrics...")
            aggregate_metrics, bias_analysis = calculate_metrics(
                test_suite_obj.test_cases, evaluation_results
            )

            # Update evaluation run
            evaluation_run.evaluation_results = evaluation_results
            evaluation_run.aggregate_metrics = aggregate_metrics
            evaluation_run.bias_analysis = bias_analysis
            evaluation_run.status = "completed"
            evaluation_run.completed_at = datetime.now()

            # Display summary
            click.echo("\n" + "=" * 60)
            click.echo("EVALUATION RESULTS SUMMARY")
            click.echo("=" * 60)

            if aggregate_metrics.successful_evaluations > 0:
                click.echo(
                    f"Overall Score (Mean): {aggregate_metrics.overall_score_mean:.1f}/100"
                )
                click.echo(
                    f"Score Range: {aggregate_metrics.overall_score_min:.1f} - {aggregate_metrics.overall_score_max:.1f}"
                )
                click.echo(
                    f"Standard Deviation: {aggregate_metrics.overall_score_std:.1f}"
                )
                click.echo(
                    f"Success Rate: {(aggregate_metrics.successful_evaluations / aggregate_metrics.total_test_cases) * 100:.1f}%"
                )

                # Show metric breakdown
                click.echo("\nMetric Breakdown:")
                for metric, scores in aggregate_metrics.metric_scores.items():
                    click.echo(
                        f"  {metric.replace('_', ' ').title()}: {scores['mean']:.1f}"
                    )

            # Show bias indicators
            if bias_analysis.bias_indicators:
                click.echo("\nBias Indicators:")
                for indicator in bias_analysis.bias_indicators:
                    click.echo(f"  • {indicator}")

            # Generate and save report
            click.echo("\n📄 Generating report...")
            reports = generate_evaluation_report(
                evaluation_run, test_suite_obj.test_cases, output_dir
            )

            click.echo("✅ Reports saved:")
            click.echo(f"   Text: {reports['text_report']}")
            click.echo(f"   JSON: {reports['json_report']}")

        except Exception as e:
            evaluation_run.status = "failed"
            evaluation_run.errors.append(str(e))
            evaluation_run.completed_at = datetime.now()
            raise e

    # Run the async function
    try:
        asyncio.run(run_evaluation_async())
    except KeyboardInterrupt:
        click.echo("\n❌ Evaluation cancelled by user")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Evaluation failed: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument("report_file")
@click.option(
    "--format",
    "output_format",
    default="summary",
    type=click.Choice(["summary", "full", "metrics"]),
)
def report(report_file, output_format):
    """Display evaluation report from file."""
    report_path = Path(report_file)

    if not report_path.exists():
        click.echo(f"❌ Report file not found: {report_path}")
        sys.exit(1)

    try:
        if report_path.suffix == ".json":
            # JSON report
            with open(report_path, "r", encoding="utf-8") as f:
                report_data = json.load(f)

            if output_format == "summary":
                # Show summary
                click.echo("EVALUATION REPORT SUMMARY")
                click.echo("=" * 30)
                click.echo(f"Run ID: {report_data['run_id']}")
                click.echo(f"Test Suite: {report_data['test_suite_name']}")
                click.echo(f"Status: {report_data['status']}")

                if report_data.get("aggregate_metrics"):
                    metrics = report_data["aggregate_metrics"]
                    click.echo(
                        f"Overall Score: {metrics['overall_score_mean']:.1f}/100"
                    )
                    click.echo(
                        f"Success Rate: {(metrics['successful_evaluations'] / metrics['total_test_cases']) * 100:.1f}%"
                    )

            elif output_format == "metrics":
                # Show detailed metrics
                if report_data.get("aggregate_metrics"):
                    metrics = report_data["aggregate_metrics"]
                    click.echo("DETAILED METRICS")
                    click.echo("=" * 20)

                    for metric, scores in metrics.get("metric_scores", {}).items():
                        click.echo(f"{metric.replace('_', ' ').title()}:")
                        click.echo(f"  Mean: {scores['mean']:.1f}")
                        click.echo(f"  Std: {scores['std']:.1f}")
                        click.echo(
                            f"  Range: {scores['min']:.1f} - {scores['max']:.1f}"
                        )
                        click.echo()

            else:  # full
                # Show full JSON
                click.echo(json.dumps(report_data, indent=2))

        else:
            # Text report
            with open(report_path, "r", encoding="utf-8") as f:
                content = f.read()
            click.echo(content)

    except Exception as e:
        click.echo(f"❌ Failed to read report: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument("test_suite_file")
@click.option("--output", "-o", help="Output file for the converted test suite")
def convert(test_suite_file, output):
    """Convert test suite to different format or validate."""
    test_suite_path = Path(test_suite_file)

    if not test_suite_path.exists():
        click.echo(f"❌ Test suite file not found: {test_suite_path}")
        sys.exit(1)

    try:
        with open(test_suite_path, "r", encoding="utf-8") as f:
            test_suite_data = json.load(f)

        # Validate by loading into model
        test_suite = TestSuite.model_validate(test_suite_data)

        click.echo("✅ Test suite is valid")
        click.echo(f"   Name: {test_suite.name}")
        click.echo(f"   Test cases: {len(test_suite.test_cases)}")
        click.echo(f"   Created: {test_suite.created_at}")

        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(
                    test_suite.model_dump(),
                    f,
                    indent=2,
                    default=str,
                    ensure_ascii=False,
                )

            click.echo(f"💾 Converted test suite saved to {output_path}")

    except Exception as e:
        click.echo(f"❌ Test suite validation failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    cli()
