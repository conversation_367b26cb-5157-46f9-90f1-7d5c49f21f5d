"""Evaluation runner for processing test cases through graph v4."""

import asyncio
import time
from typing import List

from app.core.settings import get_settings
from app.evaluation.models import CareerSuggestionResult, TestCase, TestSuite
from app.graph.v4.graph import graph
from app.services.langfuse import langfuse_handler


class EvaluationRunner:
    """Runs test cases through the graph v4 career path system."""

    def __init__(self):
        self.settings = get_settings()
        self.semaphore = asyncio.Semaphore(self.settings.evaluation_concurrent_limit)

    async def run_test_suite(
        self, test_suite: TestSuite
    ) -> List[CareerSuggestionResult]:
        """Run all test cases in a test suite through the career path system."""
        print(f"Running {len(test_suite.test_cases)} test cases...")

        # Process test cases in batches to manage memory and rate limits
        batch_size = self.settings.evaluation_batch_size
        all_results = []

        for i in range(0, len(test_suite.test_cases), batch_size):
            batch = test_suite.test_cases[i : i + batch_size]
            print(
                f"Processing batch {i // batch_size + 1}/{(len(test_suite.test_cases) + batch_size - 1) // batch_size}"
            )

            batch_results = await self._run_batch(batch)
            all_results.extend(batch_results)

            # Small delay between batches
            if i + batch_size < len(test_suite.test_cases):
                await asyncio.sleep(2)

        return all_results

    async def _run_batch(
        self, test_cases: List[TestCase]
    ) -> List[CareerSuggestionResult]:
        """Run a batch of test cases concurrently."""
        tasks = [self._run_single_test_case(test_case) for test_case in test_cases]
        return await asyncio.gather(*tasks, return_exceptions=True)

    async def _run_single_test_case(
        self, test_case: TestCase
    ) -> CareerSuggestionResult:
        """Run a single test case through the career path system."""
        async with self.semaphore:
            start_time = time.time()

            try:
                # Convert test case to input format for graph v4
                input_data = self._convert_test_case_to_input(test_case)

                # Run through graph v4 with retry logic
                result = await self._execute_with_retry(input_data)

                execution_time = time.time() - start_time

                # Handle the actual response format from graph v4 API
                responses = result.get("responses", [])
                remain_career_paths = result.get("remain_career_paths", [])

                # If responses is a dict (single response), convert to list
                if isinstance(responses, dict):
                    responses = [responses]

                # If remain_career_paths contains dicts, extract just the names
                if remain_career_paths and isinstance(remain_career_paths[0], dict):
                    remain_career_paths = [
                        item.get("career_path_name", str(item))
                        for item in remain_career_paths
                    ]

                return CareerSuggestionResult(
                    test_case_id=test_case.id,
                    responses=responses,
                    remain_career_paths=remain_career_paths,
                    execution_time=execution_time,
                    error=None,
                )

            except Exception as e:
                execution_time = time.time() - start_time
                print(f"Error processing test case {test_case.id}: {str(e)}")

                return CareerSuggestionResult(
                    test_case_id=test_case.id,
                    responses=[],
                    remain_career_paths=[],
                    execution_time=execution_time,
                    error=str(e),
                )

    def _convert_test_case_to_input(self, test_case: TestCase) -> dict:
        """Convert TestCase to the input format expected by graph v4."""
        return {
            "career_path": test_case.career_path,
            "major": test_case.major,
            "university": test_case.university,
            "skills_gained": test_case.skills_gained,
            "experience": test_case.experience,
            "language_level": test_case.language_level,
            "hobbies": test_case.hobbies,
            "characteristics": test_case.characteristics,
            "favourite_subject": test_case.favourite_subject,
            "number_output": test_case.number_output,
            "group_function": test_case.group_function,
        }

    async def _execute_with_retry(self, input_data: dict) -> dict:
        """Execute graph v4 with retry logic."""
        max_retries = self.settings.evaluation_max_retries

        for attempt in range(max_retries + 1):
            try:
                # Run through graph v4
                result = await graph.ainvoke(
                    input_data,
                    config={
                        "callbacks": [langfuse_handler],
                        "run_name": f"Evaluation Test Case - Attempt {attempt + 1}",
                    },
                )
                return result

            except Exception as e:
                if attempt == max_retries:
                    raise e

                # Exponential backoff
                wait_time = 2**attempt
                print(
                    f"Attempt {attempt + 1} failed, retrying in {wait_time} seconds..."
                )
                await asyncio.sleep(wait_time)

    async def run_single_test_case(self, test_case: TestCase) -> CareerSuggestionResult:
        """Run a single test case (useful for debugging or targeted testing)."""
        return await self._run_single_test_case(test_case)


class QuickEvaluationRunner:
    """Lightweight runner for quick evaluations without full concurrency controls."""

    async def run_test_case(self, test_case: TestCase) -> CareerSuggestionResult:
        """Run a single test case quickly."""
        start_time = time.time()

        try:
            input_data = {
                "career_path": test_case.career_path,
                "major": test_case.major,
                "university": test_case.university,
                "skills_gained": test_case.skills_gained,
                "experience": test_case.experience,
                "language_level": test_case.language_level,
                "hobbies": test_case.hobbies,
                "characteristics": test_case.characteristics,
                "favourite_subject": test_case.favourite_subject,
                "number_output": test_case.number_output,
                "group_function": test_case.group_function,
            }

            result = await graph.ainvoke(
                input_data,
                config={
                    "callbacks": [langfuse_handler],
                    "run_name": "Quick Evaluation Test",
                },
            )

            execution_time = time.time() - start_time

            return CareerSuggestionResult(
                test_case_id=test_case.id,
                responses=result.get("responses", []),
                remain_career_paths=result.get("remain_career_paths", []),
                execution_time=execution_time,
                error=None,
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return CareerSuggestionResult(
                test_case_id=test_case.id,
                responses=[],
                remain_career_paths=[],
                execution_time=execution_time,
                error=str(e),
            )


async def run_evaluation(test_suite: TestSuite) -> List[CareerSuggestionResult]:
    """Run evaluation on a test suite."""
    runner = EvaluationRunner()
    return await runner.run_test_suite(test_suite)
