"""AI evaluator using <PERSON> to assess career path recommendation quality."""

import asyncio
import time
from typing import Dict, List

from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from app.evaluation.models import (
    CareerSuggestionResult,
    EvaluationResult,
    EvaluationScore,
    TestCase,
)
from app.evaluation.gemini import get_chat_gemini


class DetailedEvaluationScore(BaseModel):
    """Detailed evaluation score from Gemini."""

    relevance_score: float = Field(
        ge=0, le=100, description="How well suggestions match user profile"
    )
    relevance_explanation: str = Field(
        description="Detailed explanation of relevance score"
    )

    personalization_score: float = Field(
        ge=0, le=100, description="How tailored recommendations are"
    )
    personalization_explanation: str = Field(
        description="Detailed explanation of personalization score"
    )

    diversity_score: float = Field(
        ge=0, le=100, description="Variety in suggested career paths"
    )
    diversity_explanation: str = Field(
        description="Detailed explanation of diversity score"
    )

    progression_logic_score: float = Field(
        ge=0, le=100, description="Logical career progression"
    )
    progression_logic_explanation: str = Field(
        description="Detailed explanation of progression logic"
    )

    skill_alignment_score: float = Field(
        ge=0, le=100, description="Match between required and existing skills"
    )
    skill_alignment_explanation: str = Field(
        description="Detailed explanation of skill alignment"
    )

    completeness_score: float = Field(
        ge=0, le=100, description="Completeness of information provided"
    )
    completeness_explanation: str = Field(
        description="Detailed explanation of completeness score"
    )

    actionability_score: float = Field(
        ge=0, le=100, description="How actionable the recommendations are"
    )
    actionability_explanation: str = Field(
        description="Detailed explanation of actionability score"
    )

    overall_assessment: str = Field(description="Overall assessment and key insights")
    strengths: List[str] = Field(description="Key strengths of the recommendations")
    weaknesses: List[str] = Field(description="Areas for improvement")
    suggestions_for_improvement: List[str] = Field(
        description="Specific suggestions for improvement"
    )


EVALUATION_PROMPT = """
You are an expert career counselor and AI system evaluator. Evaluate the quality of career path recommendations generated by an AI system.

## User Profile:
**Education:** {major} from {university}
**Experience:** {experience}
**Skills:** {skills_gained}
**Language Level:** {language_level}
**Hobbies:** {hobbies}
**Characteristics:** {characteristics}
**Favorite Subject:** {favourite_subject}
**Career Stage:** {career_stage}
**Experience Level:** {experience_level}
**Focus Area:** {focus_area}

## AI System Output:
**Career Suggestions:** {career_suggestions}
**Additional Paths:** {remain_career_paths}
**Execution Time:** {execution_time:.2f} seconds

## Evaluation Criteria:

1. **Relevance (0-100):** How well do the suggested career paths align with the user's educational background, experience, and skills?

2. **Personalization (0-100):** How well are the recommendations tailored to the user's specific characteristics, interests, and career stage?

3. **Diversity (0-100):** Is there good variety in the suggested career paths? Do they cover different industries or specializations?

4. **Progression Logic (0-100):** Do the career suggestions make logical sense given the user's current level and potential growth paths?

5. **Skill Alignment (0-100):** How well do the required skills for suggested careers match the user's existing skills and learning capacity?

6. **Completeness (0-100):** Is sufficient information provided for each career suggestion (descriptions, requirements, growth paths)?

7. **Actionability (0-100):** How actionable are the recommendations? Are clear next steps or development paths provided?

For each criterion, provide:
- A score from 0-100
- Detailed explanation of the score
- Specific examples from the output

Also provide:
- Overall assessment
- Key strengths (3-5 points)
- Key weaknesses (3-5 points)
- Specific suggestions for improvement (3-5 points)

Be objective, thorough, and constructive in your evaluation.
"""


class CareerPathEvaluator:
    """Evaluates career path recommendations using Gemini AI."""

    def __init__(self):
        self.llm = get_chat_gemini(model="gemini-2.5-flash", temperature=0.3)
        self.prompt_template = ChatPromptTemplate.from_template(EVALUATION_PROMPT)

        # Metric weights for calculating overall score
        self.metric_weights = {
            "relevance": 0.20,
            "personalization": 0.15,
            "diversity": 0.10,
            "progression_logic": 0.15,
            "skill_alignment": 0.15,
            "completeness": 0.15,
            "actionability": 0.10,
        }

    async def evaluate_result(
        self, test_case: TestCase, suggestion_result: CareerSuggestionResult
    ) -> EvaluationResult:
        """Evaluate a single suggestion result against the test case."""
        start_time = time.time()

        try:
            # Prepare the evaluation input
            evaluation_input = self._prepare_evaluation_input(
                test_case, suggestion_result
            )

            # Run evaluation through Gemini
            chain = self.prompt_template | self.llm.with_structured_output(
                DetailedEvaluationScore
            )
            detailed_score = await chain.ainvoke(evaluation_input)

            # Convert to EvaluationScore objects
            scores = self._convert_to_evaluation_scores(detailed_score)

            # Calculate overall score
            overall_score = self._calculate_overall_score(scores)

            evaluation_time = time.time() - start_time

            return EvaluationResult(
                test_case_id=test_case.id,
                suggestion_result=suggestion_result,
                scores=scores,
                overall_score=overall_score,
                evaluation_time=evaluation_time,
                evaluator_notes=detailed_score.overall_assessment,
            )

        except Exception as e:
            evaluation_time = time.time() - start_time
            print(f"Error evaluating test case {test_case.id}: {str(e)}")

            # Return default evaluation with error
            return EvaluationResult(
                test_case_id=test_case.id,
                suggestion_result=suggestion_result,
                scores=[],
                overall_score=0.0,
                evaluation_time=evaluation_time,
                evaluator_notes=f"Evaluation failed: {str(e)}",
            )

    def _prepare_evaluation_input(
        self, test_case: TestCase, suggestion_result: CareerSuggestionResult
    ) -> Dict:
        """Prepare input for the evaluation prompt."""
        # Extract career suggestions from the result
        career_suggestions = []
        if suggestion_result.responses:
            for response in suggestion_result.responses:
                if isinstance(response, dict):
                    career_suggestions.append(response)

        return {
            "major": test_case.major,
            "university": test_case.university,
            "experience": test_case.experience,
            "skills_gained": test_case.skills_gained,
            "language_level": test_case.language_level,
            "hobbies": test_case.hobbies,
            "characteristics": test_case.characteristics,
            "favourite_subject": test_case.favourite_subject,
            "career_stage": test_case.career_stage.value,
            "experience_level": test_case.experience_level.value,
            "focus_area": test_case.focus_area,
            "career_suggestions": career_suggestions,
            "remain_career_paths": suggestion_result.remain_career_paths,
            "execution_time": suggestion_result.execution_time,
        }

    def _convert_to_evaluation_scores(
        self, detailed_score: DetailedEvaluationScore
    ) -> List[EvaluationScore]:
        """Convert detailed scores to EvaluationScore objects."""
        return [
            EvaluationScore(
                metric_name="relevance",
                score=detailed_score.relevance_score,
                explanation=detailed_score.relevance_explanation,
            ),
            EvaluationScore(
                metric_name="personalization",
                score=detailed_score.personalization_score,
                explanation=detailed_score.personalization_explanation,
            ),
            EvaluationScore(
                metric_name="diversity",
                score=detailed_score.diversity_score,
                explanation=detailed_score.diversity_explanation,
            ),
            EvaluationScore(
                metric_name="progression_logic",
                score=detailed_score.progression_logic_score,
                explanation=detailed_score.progression_logic_explanation,
            ),
            EvaluationScore(
                metric_name="skill_alignment",
                score=detailed_score.skill_alignment_score,
                explanation=detailed_score.skill_alignment_explanation,
            ),
            EvaluationScore(
                metric_name="completeness",
                score=detailed_score.completeness_score,
                explanation=detailed_score.completeness_explanation,
            ),
            EvaluationScore(
                metric_name="actionability",
                score=detailed_score.actionability_score,
                explanation=detailed_score.actionability_explanation,
            ),
        ]

    def _calculate_overall_score(self, scores: List[EvaluationScore]) -> float:
        """Calculate weighted overall score."""
        if not scores:
            return 0.0

        weighted_sum = 0.0
        total_weight = 0.0

        for score in scores:
            weight = self.metric_weights.get(score.metric_name, 0.0)
            weighted_sum += score.score * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    async def evaluate_batch(
        self,
        test_cases: List[TestCase],
        suggestion_results: List[CareerSuggestionResult],
    ) -> List[EvaluationResult]:
        """Evaluate a batch of results."""
        # Create pairs of test cases and results
        pairs = []
        result_map = {result.test_case_id: result for result in suggestion_results}

        for test_case in test_cases:
            if test_case.id in result_map:
                pairs.append((test_case, result_map[test_case.id]))

        # Process with limited concurrency
        semaphore = asyncio.Semaphore(3)  # Limit concurrent evaluations

        async def evaluate_pair(test_case, suggestion_result):
            async with semaphore:
                return await self.evaluate_result(test_case, suggestion_result)

        tasks = [evaluate_pair(tc, sr) for tc, sr in pairs]
        return await asyncio.gather(*tasks, return_exceptions=True)


async def evaluate_suggestions(
    test_cases: List[TestCase], suggestion_results: List[CareerSuggestionResult]
) -> List[EvaluationResult]:
    """Evaluate career path suggestions using Gemini AI."""
    evaluator = CareerPathEvaluator()
    return await evaluator.evaluate_batch(test_cases, suggestion_results)
