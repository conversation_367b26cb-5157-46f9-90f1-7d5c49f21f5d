from functools import lru_cache
from typing import Optional

import httpx
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI

load_dotenv()

# Shared HTTP clients for connection pooling optimization
_sync_client: Optional[httpx.Client] = None
_async_client: Optional[httpx.AsyncClient] = None


def _get_shared_http_clients() -> tuple[httpx.Client, httpx.AsyncClient]:
    """Get shared HTTP clients for connection pooling optimization."""
    global _sync_client, _async_client

    if _sync_client is None or _async_client is None:
        # Configure connection limits for optimal performance
        timeout = httpx.Timeout(timeout=60, connect=5)
        limits = httpx.Limits(max_connections=200, max_keepalive_connections=50)

        _sync_client = httpx.Client(timeout=timeout, limits=limits)
        _async_client = httpx.AsyncClient(timeout=timeout, limits=limits)

    return _sync_client, _async_client


@lru_cache(maxsize=32)
def get_chat_gemini(
    model: str = "gemini-2.5-flash",
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    **kwargs,
) -> ChatGoogleGenerativeAI:
    """
    Get an optimized ChatGoogleGenerativeAI instance with shared HTTP client for connection pooling.

    Args:
        model: Google Gemini model name (default: "gemini-2.5-flash")
        temperature: Sampling temperature (default: None, uses Google default)
        max_tokens: Maximum tokens to generate (default: None, uses Google default)
        **kwargs: Additional ChatGoogleGenerativeAI parameters

    Returns:
        Configured ChatGoogleGenerativeAI instance with optimized HTTP client
    """
    sync_client, async_client = _get_shared_http_clients()

    # Build ChatGoogleGenerativeAI parameters
    llm_kwargs = {
        "model": model,
        **kwargs,
    }

    # Only add parameters if they're explicitly set to avoid overriding defaults
    if temperature is not None:
        llm_kwargs["temperature"] = temperature
    if max_tokens is not None:
        llm_kwargs["max_output_tokens"] = max_tokens

    return ChatGoogleGenerativeAI(**llm_kwargs)


async def cleanup_gemini_http_clients():
    """Clean up shared HTTP clients. Call this on application shutdown."""
    global _sync_client, _async_client

    if _sync_client:
        _sync_client.close()
        _sync_client = None

    if _async_client:
        await _async_client.aclose()
        _async_client = None
