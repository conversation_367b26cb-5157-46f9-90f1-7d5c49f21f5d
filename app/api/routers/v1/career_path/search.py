from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.schemas.career_path import CareerPathSearchRequest
from app.handlers.search_career_path import search_career_path

career_path_search_router = APIRouter(prefix="/internal/v1", tags=["Career Path"])


@career_path_search_router.post("/search")
async def career_path_search(data: CareerPathSearchRequest):
    try:
        results, total = await search_career_path(data)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": results,
                "metadata": {"total": total},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )
