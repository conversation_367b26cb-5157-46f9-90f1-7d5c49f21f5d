import asyncio
import json
import os

from dotenv import load_dotenv
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.schemas.career_path import CareerPathRequest
from app.graph.v2.graph import graph
from app.handlers.recommendation_career_path import recommendation_career_path
from app.helpers import get_user_profile, hash_dictionary
from app.models.suggest_career_path import CareerPathRecommendationInput
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.langfuse import langfuse_handler
from app.services.redis import redis_service

load_dotenv()


career_path_router = APIRouter(prefix="/internal/v1", tags=["Career Path"])


@career_path_router.post("/suggestion")
async def career_path_suggestion(data: CareerPathRequest):
    user_profile = await get_user_profile(data.user_id)
    data_input = {
        "major": user_profile.get("major"),
        "skills_gained": user_profile.get("skills_gained"),
        "experience": user_profile.get("experience"),
        "university": user_profile.get("university"),
        "language_level": user_profile.get("language_level"),
        "hobbies": user_profile.get("hobbies"),
        "characteristics": user_profile.get("characteristics"),
        "favourite_subject": user_profile.get("favourite_subject"),
        "number_output": data.number_output,
    }

    if data.job_title:
        input = {"career_path": data.job_title, **data_input}
    else:
        input = data_input

    # Check if the response is cached
    cache_key = {
        "user_id": data.user_id,
        "career_path": data.job_title if data.job_title else "",
    }
    try:
        redis_key = f"career_path_suggestion_{hash_dictionary(cache_key)}"
        cached_response = await redis_service.get(redis_key)
    except Exception as e:
        print(e)
        cached_response = None

    if cached_response:
        output = json.loads(cached_response)
    else:
        try:
            response = await graph.ainvoke(
                input,
                config={
                    "callbacks": [langfuse_handler],
                    "run_name": "Career Path",
                    "metadata": {"user_id": data.user_id},
                },
            )
            output = response["responses"]
        except Exception as e:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"code": 500, "error": str(e), "data": None, "metadata": {}},
            )
        if len(output) == 0:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "code": 400,
                    "error": "No career path suggestion found",
                    "data": [],
                    "metadata": {},
                },
            )

        # Cache the response
        try:
            if data.job_title:
                cache_key = {
                    "user_id": data.user_id,
                    "career_path": data.job_title,
                }
                redis_key = f"career_path_suggestion_{hash_dictionary(cache_key)}"
                await redis_service.set(
                    redis_key,
                    json.dumps(output),
                    ex=int(os.getenv("REDIS_TTL")),
                )
            else:
                await redis_service.set(
                    redis_key,
                    json.dumps(output),
                    ex=int(os.getenv("REDIS_TTL")),
                )
                cache_tasks = []
                for response_item in output:
                    cache_key = {
                        "user_id": data.user_id,
                        "career_path": response_item["career_path"],
                    }
                    redis_key = f"career_path_suggestion_{hash_dictionary(cache_key)}"
                    cache_tasks.append(
                        redis_service.set(
                            redis_key,
                            json.dumps([response_item]),
                            ex=int(os.getenv("REDIS_TTL")),
                        )
                    )

                if cache_tasks:
                    await asyncio.gather(*cache_tasks)
        except Exception as e:
            print(e)

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "code": 200,
            "error": None,
            "data": output,
            "metadata": {},
        },
    )


@career_path_router.post("/recommendation")
async def career_path_recommendation(data: CareerPathRecommendationInput):
    data_input = {"major": data.major, "number_output": data.number_output}

    # Generate cache key
    cache_key = generate_cache_key("recommendation_v1", data_input)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": cached_result["career_paths"],
                "metadata": {
                    "major": data.major,
                    "group_function": cached_result["group_function"],
                },
            },
        )

    try:
        response = await recommendation_career_path.ainvoke(
            data_input,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Career Path Recommendation",
            },
        )
        career_paths = response["career_paths"]

        # Store in cache
        cache_data = {
            "career_paths": career_paths,
            "group_function": response["group_function"],
        }
        await cache_set(cache_key, cache_data)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": career_paths,
                "metadata": {
                    "major": data.major,
                    "group_function": response["group_function"],
                },
            },
        )
    except ValueError as e:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"code": 400, "error": str(e), "data": None, "metadata": {}},
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )
