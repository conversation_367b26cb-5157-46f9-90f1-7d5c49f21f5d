from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.graph.v4.chains.detailed_info import get_detailed_info
from app.graph.v4.graph import graph as suggest_graph
from app.graph.v4.utils import calculate_matching_score, build_user_profile
from app.models.suggest_career_path import CareerPathInputV2
from app.services.cache import (
    CacheType,
    cache_get_with_type,
    cache_set_with_type,
    generate_cache_key,
)
from app.services.langfuse import langfuse_handler

career_path_router_v4 = APIRouter(prefix="/internal/v4", tags=["Career Path V4"])


@career_path_router_v4.post("/suggestion")
async def career_path_suggestion(data: CareerPathInputV2):
    # Convert Pydantic model to dict once, avoiding data duplication
    # Exclude unset/None values to maintain backward compatibility
    input_data = data.model_dump(exclude_unset=True)

    # Generate cache key
    cache_key = generate_cache_key("suggestion_v4", input_data)

    # Try to get from cache first
    cached_result = await cache_get_with_type(cache_key, CacheType.MONGODB)
    if cached_result:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": cached_result,
                "metadata": {},
            },
        )

    try:
        response = await suggest_graph.ainvoke(
            input_data,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Career Path Suggestion V4",
            },
        )

        # Modify response directly instead of creating new dict
        result = response["responses"]
        result["remain_career_paths"] = response["remain_career_paths"]

        # Store in cache (non-blocking, doesn't affect response time)
        await cache_set_with_type(cache_key, result, CacheType.MONGODB)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": result,
                "metadata": {},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )


@career_path_router_v4.post("/detailed-info")
async def career_path_detailed_info(data: CareerPathInputV2):
    # Convert Pydantic model to dict once, avoiding data duplication
    # Exclude unset/None values to maintain backward compatibility
    data_input = data.model_dump(exclude_unset=True)

    # Generate cache key
    cache_key = generate_cache_key("detailed_info_v4", data_input)

    # Try to get from cache first
    cached_result = await cache_get_with_type(cache_key, CacheType.MONGODB)
    if cached_result:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": cached_result,
                "metadata": {},
            },
        )
    try:
        user_profile = build_user_profile(data_input)
        career_path = data_input["career_path"]

        matching_score, _, _ = await calculate_matching_score(career_path, user_profile)

        # Generate "why you fit" analysis using cached chain objects
        response = await get_detailed_info(career_path, user_profile, matching_score)

        # Store in cache (non-blocking, doesn't affect response time)
        await cache_set_with_type(cache_key, response, CacheType.MONGODB)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": response,
                "metadata": {},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )
