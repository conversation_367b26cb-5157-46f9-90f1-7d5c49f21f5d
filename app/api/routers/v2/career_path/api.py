from dotenv import load_dotenv
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.graph.v2.eval_graph import graph as eval_graph
from app.graph.v2.graph import graph as suggest_graph
from app.handlers.fetch_career_path import process_get_career_path_by_ids
from app.models.fetch_career_path import CareerPathFetchRequest
from app.models.suggest_career_path import CareerPathInputV2
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.langfuse import langfuse_handler

load_dotenv()

career_path_router_v2 = APIRouter(prefix="/internal/v2", tags=["Career Path"])


@career_path_router_v2.post("/suggestion")
async def career_path_suggestion(data: CareerPathInputV2):
    data_input = {
        "major": data.major,
        "skills_gained": data.skills_gained,
        "experience": data.experience,
        "university": data.university,
        "language_level": data.language_level,
        "hobbies": data.hobbies,
        "characteristics": data.characteristics,
        "favourite_subject": data.favourite_subject,
        "number_output": data.number_output,
    }

    if data.career_path:
        input = {"career_path": data.career_path, **data_input}
    else:
        input = data_input

    # Generate cache key
    cache_key = generate_cache_key("suggestion_v2", input)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": cached_result,
                "metadata": {},
            },
        )

    try:
        response = await suggest_graph.ainvoke(
            input,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Career Path Suggestion V2",
            },
        )

        result = response["responses"]

        # Store in cache (non-blocking, doesn't affect response time)
        await cache_set(cache_key, result)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": result,
                "metadata": {},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )


@career_path_router_v2.post("/evaluation")
async def career_path_evaluation(data: CareerPathInputV2):
    data_input = {
        "major": data.major,
        "skills_gained": data.skills_gained,
        "experience": data.experience,
        "university": data.university,
        "language_level": data.language_level,
        "hobbies": data.hobbies,
        "characteristics": data.characteristics,
        "favourite_subject": data.favourite_subject,
        "number_output": data.number_output,
    }

    input = {
        "career_path": data.career_path,
        "group_function": data.group_function,
        **data_input,
    }

    # Generate cache key
    cache_key = generate_cache_key("evaluation_v2", input)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": cached_result,
                "metadata": {},
            },
        )

    try:
        response = await eval_graph.ainvoke(
            input,
            config={
                "callbacks": [langfuse_handler],
                "run_name": "Career Path Evaluation V2",
            },
        )

        # Store in cache (non-blocking, doesn't affect response time)
        await cache_set(cache_key, response)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": response,
                "metadata": {},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )


@career_path_router_v2.post("/fetch-by-ids")
async def fetch_career_path_by_ids(request: CareerPathFetchRequest):
    try:
        results = await process_get_career_path_by_ids(request.ids)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "message": "Request processed successfully.",
                "data": results,
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "error", "message": str(e), "data": None},
        )
