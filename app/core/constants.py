LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",  # Default handler logs INFO and above
            "formatter": "default",
            "class": "logging.StreamHandler",
        },
        "access": {
            "level": "INFO",
            "formatter": "default",
            "class": "logging.FileHandler",
            "filename": "logs/access.log",
        },
        "error": {
            "level": "INFO",
            "formatter": "default",
            "class": "logging.FileHandler",
            "filename": "logs/error.log",
        },
    },
    "loggers": {
        "uvicorn": {
            "handlers": ["default"],
            "level": "INFO",  # Logs INFO and above
            "propagate": False,
        },
        "uvicorn.access": {
            "handlers": ["default"],
            "level": "INFO",  # Logs INFO and above
            "propagate": False,
        },
        "uvicorn.error": {
            "handlers": ["default"],
            "level": "INFO",  # Logs ERROR and above
            "propagate": False,
        },
    },
}
