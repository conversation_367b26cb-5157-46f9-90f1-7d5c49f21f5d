from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    app_env: str = "dev"
    app_host: str = "0.0.0.0"
    app_port: int = 8080
    sentry_dsn: str = ""

    # Cache feature flags
    cache_redis_enabled: bool = True
    cache_mongodb_enabled: bool = True

    # Google API configuration
    google_api_key: str = ""

    # Evaluation system settings
    evaluation_batch_size: int = 10
    evaluation_timeout: int = 300
    evaluation_max_retries: int = 3
    evaluation_concurrent_limit: int = 5


@lru_cache
def get_settings():
    settings = Settings()
    return settings
