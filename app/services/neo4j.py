import os
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase, AsyncDriver

load_dotenv()

# Global driver instance for connection pooling
_neo4j_driver: Optional[AsyncDriver] = None


def get_neo4j_driver() -> AsyncDriver:
    """Get or create Neo4j driver with optimized connection pooling."""
    global _neo4j_driver

    if _neo4j_driver is None:
        uri = os.getenv("NEO4J_URI")
        user = os.getenv("NEO4J_USER")
        password = os.getenv("NEO4J_PASSWORD")

        if not all([uri, user, password]):
            raise ValueError("Neo4j connection parameters not properly configured")

        # Configure driver with optimal pool settings
        _neo4j_driver = AsyncGraphDatabase.driver(
            uri,
            auth=(user, password),
            connection_timeout=int(os.getenv("NEO4J_CONNECTION_TIMEOUT", "5")),
            max_connection_pool_size=int(
                os.getenv("NEO4J_MAX_CONNECTION_POOL_SIZE", "50")
            ),
            connection_acquisition_timeout=int(
                os.getenv("NEO4J_ACQUISITION_TIMEOUT", "60")
            ),
            max_transaction_retry_time=int(os.getenv("NEO4J_MAX_RETRY_TIME", "30")),
            keep_alive=True,
        )

    return _neo4j_driver


async def cleanup_neo4j():
    """Clean up Neo4j driver. Call this on application shutdown."""
    global _neo4j_driver

    if _neo4j_driver:
        await _neo4j_driver.close()
        _neo4j_driver = None


class Neo4jService:
    def __init__(self, driver: Optional[AsyncDriver] = None):
        """Initialize with global driver for connection pooling."""
        self._driver = driver or get_neo4j_driver()
        self._owns_driver = driver is None  # Track if we created the driver

    async def connect(self) -> None:
        """No-op since we use global driver with pooling."""
        # Connection test removed - pool handles connection management
        pass

    async def close(self) -> None:
        """Only close if we don't own the driver (for backward compatibility)."""
        # Don't close the global driver
        pass

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()

    async def execute_query(
        self, query: str, parameters: Optional[Dict[str, Any]] = None
    ) -> List[Any]:
        """Run a Cypher query asynchronously.

        Args:
            query: Cypher query string
            parameters: Optional query parameters

        Returns:
            Query results as a list
        """
        if parameters is None:
            parameters = {}

        try:
            async with self._driver.session() as session:
                result = await session.run(query, parameters)
                records = await result.data()
                await result.consume()
                return records
        except Exception as e:
            raise Exception(f"Query execution failed: {str(e)}")


@asynccontextmanager
async def get_neo4j_service():
    """Get Neo4j service with global connection pooling."""
    service = Neo4jService()  # Uses global driver
    await service.connect()
    try:
        yield service
    finally:
        await service.close()
