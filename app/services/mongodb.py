import logging
import os
from contextlib import asynccontextmanager
from typing import Optional

from dotenv import load_dotenv
from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)

load_dotenv()

logger = logging.getLogger(__name__)

# MongoDB configuration
MONGODB_URI = os.getenv("MONGODB_URI")
MONGODB_DB = os.getenv("MONGODB_DB")

# Global MongoDB client (initialized on first use)
_mongo_client: Optional[AsyncIOMotorClient] = None
_mongo_db: Optional[AsyncIOMotorDatabase] = None


def get_mongo_client() -> AsyncIOMotorClient:
    """Get or create MongoDB client instance with optimized pooling"""
    global _mongo_client
    if _mongo_client is None:
        if not MONGODB_URI:
            raise ValueError("MONGODB_URI environment variable is not set")
        _mongo_client = AsyncIOMotorClient(
            MONGODB_URI,
            serverSelectionTimeoutMS=5000,  # 5 seconds to select server
            connectTimeoutMS=3000,  # 3 seconds to connect
            socketTimeoutMS=3000,  # 3 seconds for socket operations
            maxPoolSize=int(
                os.getenv("MONGODB_MAX_POOL_SIZE", "50")
            ),  # Increased pool size
            minPoolSize=int(
                os.getenv("MONGODB_MIN_POOL_SIZE", "10")
            ),  # Maintain minimum connections
            maxIdleTimeMS=120000,  # Close idle connections after 2 minutes
            retryWrites=True,  # Enable retry for better resilience
            retryReads=True,  # Enable retry for better resilience
        )
    return _mongo_client


def get_mongo_database() -> AsyncIOMotorDatabase:
    """Get MongoDB database instance"""
    global _mongo_db
    if _mongo_db is None:
        if not MONGODB_DB:
            raise ValueError("MONGODB_DB environment variable is not set")
        client = get_mongo_client()
        _mongo_db = client[MONGODB_DB]
    return _mongo_db


def get_cache_collection() -> AsyncIOMotorCollection:
    """Get the cache collection for storing cache data"""
    db = get_mongo_database()
    return db.cache


@asynccontextmanager
async def get_mongo_service():
    """Context manager for MongoDB operations with proper connection handling"""
    try:
        # Test connection
        client = get_mongo_client()
        await client.admin.command("ping")
        yield get_cache_collection()
    except Exception as e:
        logger.error(f"MongoDB connection error: {str(e)}")
        raise


async def close_mongo_connection():
    """Close MongoDB connection (useful for cleanup)"""
    global _mongo_client, _mongo_db
    if _mongo_client:
        _mongo_client.close()
        _mongo_client = None
        _mongo_db = None


async def cleanup_mongodb():
    """Async cleanup for MongoDB connection. Call this on application shutdown."""
    await close_mongo_connection()
    logger.info("MongoDB connection pool closed")
