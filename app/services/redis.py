import os
from typing import Optional

from dotenv import load_dotenv
from redis.asyncio import ConnectionPool, Redis

load_dotenv()

# Global connection pool for Redis
_redis_pool: Optional[ConnectionPool] = None
_redis_client: Optional[Redis] = None


def get_redis_pool() -> ConnectionPool:
    """Get or create a Redis connection pool with optimized settings."""
    global _redis_pool

    if _redis_pool is None:
        redis_uri = os.getenv("REDIS_URI")
        if not redis_uri:
            raise ValueError("REDIS_URI environment variable is not set")

        # Configure connection pool with optimal settings
        _redis_pool = ConnectionPool.from_url(
            redis_uri, max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
        )

    return _redis_pool


def get_redis_client() -> Redis:
    """Get Redis client with connection pooling."""
    global _redis_client

    if _redis_client is None:
        pool = get_redis_pool()
        _redis_client = Redis(connection_pool=pool)

    return _redis_client


# Create the default Redis service instance with pooling
redis_service = get_redis_client()

# Default TTL for cache entries in seconds (1 hour)
CACHE_TTL = int(os.getenv("REDIS_TTL", 3600))


async def cleanup_redis():
    """Clean up Redis connection pool. Call this on application shutdown."""
    global _redis_client, _redis_pool

    if _redis_client:
        await _redis_client.aclose()
        _redis_client = None

    if _redis_pool:
        await _redis_pool.aclose()
        _redis_pool = None
