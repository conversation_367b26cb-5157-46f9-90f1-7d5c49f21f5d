from functools import lru_cache
from typing import Optional

import httpx
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

load_dotenv()

# Shared HTTP clients for connection pooling optimization
_sync_client: Optional[httpx.Client] = None
_async_client: Optional[httpx.AsyncClient] = None


def _get_shared_http_clients() -> tuple[httpx.Client, httpx.AsyncClient]:
    """Get shared HTTP clients for connection pooling optimization."""
    global _sync_client, _async_client

    if _sync_client is None or _async_client is None:
        # Configure connection limits for optimal performance

        timeout = httpx.Timeout(timeout=60, connect=5)  # 10 seconds timeout
        limits = httpx.Limits(max_connections=200, max_keepalive_connections=50)

        _sync_client = httpx.Client(timeout=timeout, limits=limits)
        _async_client = httpx.AsyncClient(timeout=timeout, limits=limits)

    return _sync_client, _async_client


@lru_cache(maxsize=32)
def get_chat_openai(
    model: str = "gpt-4o",
    temperature: Optional[float] = None,
    timeout: Optional[float] = None,
    max_tokens: Optional[int] = None,
    **kwargs,
) -> ChatOpenAI:
    """
    Get an optimized ChatOpenAI instance with shared HTTP client for connection pooling.

    Args:
        model: OpenAI model name (default: "gpt-4o")
        temperature: Sampling temperature (default: None, uses OpenAI default)
        timeout: Request timeout in seconds (default: None, uses client default)
        max_tokens: Maximum tokens to generate (default: None, uses OpenAI default)
        **kwargs: Additional ChatOpenAI parameters

    Returns:
        Configured ChatOpenAI instance with optimized HTTP client
    """
    sync_client, async_client = _get_shared_http_clients()

    # Build ChatOpenAI parameters
    llm_kwargs = {
        "model": model,
        "http_client": sync_client,
        "http_async_client": async_client,
        **kwargs,
    }

    # Only add parameters if they're explicitly set to avoid overriding defaults
    if temperature is not None:
        llm_kwargs["temperature"] = temperature
    if timeout is not None:
        llm_kwargs["timeout"] = timeout
    if max_tokens is not None:
        llm_kwargs["max_tokens"] = max_tokens

    return ChatOpenAI(**llm_kwargs)


async def cleanup_http_clients():
    """Clean up shared HTTP clients. Call this on application shutdown."""
    global _sync_client, _async_client

    if _sync_client:
        _sync_client.close()
        _sync_client = None

    if _async_client:
        await _async_client.aclose()
        _async_client = None
