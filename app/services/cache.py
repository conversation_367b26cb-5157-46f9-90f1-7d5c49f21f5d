import hashlib
import json
import logging
from enum import Enum
from typing import Any, Optional

from redis.exceptions import RedisError

from app.core.settings import get_settings
from app.services.mongo_cache import mongo_cache_get, mongo_cache_set
from app.services.redis import CACHE_TTL, redis_service

logger = logging.getLogger(__name__)


class CacheType(Enum):
    """Cache type enum for hybrid cache selection"""

    REDIS = "redis"
    MONGODB = "mongodb"


async def cache_get(key: str) -> dict | None:
    """Get data from Redis cache with error handling"""
    settings = get_settings()
    if not settings.cache_redis_enabled:
        return None

    try:
        data = await redis_service.get(key)
        if data:
            return json.loads(data)
        return None
    except RedisError as e:
        logger.error(f"Redis error when getting cache: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error when getting cache: {str(e)}")
        return None


async def cache_set(key: str, value: dict, expire: int = CACHE_TTL) -> bool:
    """Set data to Redis cache with error handling"""
    settings = get_settings()
    if not settings.cache_redis_enabled:
        return True  # Return True to indicate "success" even when disabled

    try:
        await redis_service.set(key, json.dumps(value), ex=expire)
        return True
    except RedisError as e:
        logger.error(f"Redis error when setting cache: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when setting cache: {str(e)}")
        return False


def generate_cache_key(prefix: str, data: dict) -> str:
    """Generate a cache key based on input data"""
    sorted_data = {k: data[k] for k in sorted(data.keys()) if data[k] is not None}
    data_str = json.dumps(sorted_data, sort_keys=True, ensure_ascii=False)
    return (
        f"{prefix}:{hashlib.md5(data_str.encode(), usedforsecurity=False).hexdigest()}"
    )


async def cache_get_with_type(key: str, cache_type: CacheType) -> Any | None:
    """Get data from cache with specified cache type"""
    settings = get_settings()

    if cache_type == CacheType.REDIS:
        if not settings.cache_redis_enabled:
            return None
        return await cache_get(key)
    elif cache_type == CacheType.MONGODB:
        if not settings.cache_mongodb_enabled:
            return None
        return await mongo_cache_get(key)
    else:
        logger.error(f"Unsupported cache type: {cache_type}")
        return None


async def cache_set_with_type(
    key: str, value: Any, cache_type: CacheType, expire: Optional[int] = None
) -> bool:
    """Set data to cache with specified cache type"""
    settings = get_settings()

    if cache_type == CacheType.REDIS:
        if not settings.cache_redis_enabled:
            return True  # Return True to indicate "success" even when disabled
        # Use provided expire or default CACHE_TTL for Redis
        expire_time = expire if expire is not None else CACHE_TTL
        return await cache_set(key, value, expire_time)
    elif cache_type == CacheType.MONGODB:
        if not settings.cache_mongodb_enabled:
            return True  # Return True to indicate "success" even when disabled
        # MongoDB doesn't use TTL - ignore expire parameter
        return await mongo_cache_set(key, value)
    else:
        logger.error(f"Unsupported cache type: {cache_type}")
        return False
