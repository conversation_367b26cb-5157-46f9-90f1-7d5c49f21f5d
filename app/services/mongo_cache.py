import logging
from typing import Any
from datetime import datetime, timezone

from pymongo.errors import PyMongoError

from app.core.settings import get_settings
from app.services.mongodb import get_mongo_service

logger = logging.getLogger(__name__)


async def mongo_cache_get(key: str) -> Any | None:
    """Get data from MongoDB cache with error handling"""
    settings = get_settings()
    if not settings.cache_mongodb_enabled:
        return None

    try:
        async with get_mongo_service() as collection:
            document = await collection.find_one({"_id": key})
            if document and "data" in document:
                # Return only the cached data, not metadata
                return document["data"]
            return None
    except PyMongoError as e:
        logger.error(f"MongoDB error when getting cache: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error when getting cache: {str(e)}")
        return None


async def mongo_cache_set(key: str, value: Any) -> bool:
    """Set data to MongoDB cache with error handling (no TTL - permanent storage)"""
    settings = get_settings()
    if not settings.cache_mongodb_enabled:
        return True  # Return True to indicate "success" even when disabled

    try:
        async with get_mongo_service() as collection:
            # Create document with metadata - store actual data in "data" field
            document = {
                "_id": key,
                "created_at": datetime.now(timezone.utc),
                "data": value,  # Store the cache data in a dedicated field
            }

            # Use upsert to replace existing document or create new one
            await collection.replace_one({"_id": key}, document, upsert=True)
            return True
    except PyMongoError as e:
        logger.error(f"MongoDB error when setting cache: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when setting cache: {str(e)}")
        return False


async def mongo_cache_exists(key: str) -> bool:
    """Check if a cache key exists in MongoDB"""
    settings = get_settings()
    if not settings.cache_mongodb_enabled:
        return False

    try:
        async with get_mongo_service() as collection:
            count = await collection.count_documents({"_id": key}, limit=1)
            return count > 0
    except PyMongoError as e:
        logger.error(f"MongoDB error when checking cache existence: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when checking cache existence: {str(e)}")
        return False


async def mongo_cache_delete(key: str) -> bool:
    """Delete a cache entry from MongoDB (useful for cache invalidation)"""
    settings = get_settings()
    if not settings.cache_mongodb_enabled:
        return True  # Return True to indicate "success" even when disabled

    try:
        async with get_mongo_service() as collection:
            result = await collection.delete_one({"_id": key})
            return result.deleted_count > 0
    except PyMongoError as e:
        logger.error(f"MongoDB error when deleting cache: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when deleting cache: {str(e)}")
        return False


async def mongo_cache_clear_all() -> bool:
    """Clear all cache entries from MongoDB (useful for maintenance)"""
    settings = get_settings()
    if not settings.cache_mongodb_enabled:
        return True  # Return True to indicate "success" even when disabled

    try:
        async with get_mongo_service() as collection:
            await collection.delete_many({})
            return True
    except PyMongoError as e:
        logger.error(f"MongoDB error when clearing all cache: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when clearing all cache: {str(e)}")
        return False
