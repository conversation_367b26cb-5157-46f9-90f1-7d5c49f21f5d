from typing import Optional

from pydantic import BaseModel


class CareerPathInput(BaseModel):
    job_title: Optional[str] = None
    major: str
    experience: str
    skills_gained: str
    university: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str


class CareerPathInputV2(BaseModel):
    career_path: Optional[str] = None
    group_function: Optional[str] = None
    major: str
    experience: str
    skills_gained: str
    university: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str
    number_output: int = 8


class CareerPathRecommendationInput(BaseModel):
    major: str
    number_output: int = 8
