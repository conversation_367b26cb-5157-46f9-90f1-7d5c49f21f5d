# V4 Graph and API Test Suite

This directory contains comprehensive tests for the V4 career path graph and API implementation.

## Test Structure

```
tests/
├── conftest.py                     # Shared test fixtures and configuration
├── test_runner.py                  # Test runner script
├── unit/                          # Unit tests
│   ├── graph/v4/
│   │   ├── nodes/                 # Tests for graph nodes
│   │   │   ├── test_suggest_career_path.py
│   │   │   ├── test_get_career_path.py
│   │   │   └── test_postprocess_career_path.py
│   │   ├── utils/                 # Tests for utility functions
│   │   │   └── test_calculations.py
│   │   └── test_graph.py          # Tests for graph workflow
│   └── api/v4/
│       └── test_api.py            # Tests for API endpoints
└── integration/
    └── test_v4_workflow.py        # End-to-end integration tests
```

## Test Categories

### 1. Unit Tests

**Graph Node Tests:**
- `test_suggest_career_path.py`: Tests career path suggestion logic, LLM integration, parallel evaluation
- `test_get_career_path.py`: Tests career path data retrieval from Neo4j, skills processing
- `test_postprocess_career_path.py`: Tests response formatting, salary calculations, caching

**Utility Tests:**
- `test_calculations.py`: Tests matching score calculations, evaluation processing, caching

**Graph Workflow Tests:**
- `test_graph.py`: Tests state transitions, conditional routing, node execution order

**API Tests:**
- `test_api.py`: Tests FastAPI endpoints, request/response handling, error scenarios

### 2. Integration Tests

**End-to-End Workflow Tests:**
- Complete career path suggestion workflow
- API to graph integration
- Database integration (Neo4j, MongoDB, Redis)
- LLM service integration
- Caching behavior across layers
- Error propagation
- State management

## Key Features Tested

### Functional Testing
- ✅ Career path suggestion algorithm
- ✅ Matching score calculation with different experience levels
- ✅ Skills processing and categorization
- ✅ Job progression data retrieval
- ✅ Salary calculations
- ✅ User profile formatting

### Performance Testing
- ✅ Parallel evaluation of career paths
- ✅ Caching mechanisms (Redis, MongoDB)
- ✅ Database query optimization
- ✅ Deterministic statistics generation

### Error Handling
- ✅ Invalid LLM responses
- ✅ Missing Neo4j data
- ✅ Cache failures
- ✅ Network timeouts
- ✅ API validation errors

### Integration Testing
- ✅ API → Graph → Database flow
- ✅ State propagation between nodes
- ✅ Data format consistency
- ✅ External service mocking
- ✅ Langfuse observability integration

## Test Fixtures

The test suite includes comprehensive fixtures for:

- **User Input Data**: Various user profiles and career scenarios
- **Neo4j Mock Data**: Career paths, job progressions, skills data
- **LLM Mock Responses**: Structured outputs for different chains
- **Evaluation Results**: Scoring and assessment data
- **Cache Scenarios**: Hit/miss patterns for different cache layers

## Running Tests

### Using the Test Runner

```bash
# Run all tests
uv run python tests/test_runner.py --type all

# Run only unit tests
uv run python tests/test_runner.py --type unit

# Run only integration tests
uv run python tests/test_runner.py --type integration

# Run tests with coverage
uv run python tests/test_runner.py --type coverage
```

### Using pytest directly

```bash
# Run all tests
uv run pytest tests/ -v

# Run specific test file
uv run pytest tests/unit/graph/v4/nodes/test_suggest_career_path.py -v

# Run with coverage
uv run pytest tests/ --cov=app.graph.v4 --cov=app.api.routers.v4 --cov-report=html
```

### Test Configuration

Tests are configured in `pyproject.toml`:

```toml
[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
```

## Test Data and Mocking Strategy

### External Dependencies Mocked:
- **Neo4j Database**: Mock service with realistic data responses
- **LLM Services**: Mock chains with structured outputs
- **Cache Services**: Mock Redis and MongoDB operations
- **External APIs**: Mock user profile and market data services

### Realistic Test Data:
- Vietnamese career path names and descriptions
- Realistic salary ranges and market outlook data
- Authentic user profiles with various backgrounds
- Real-world skill sets and job progressions

## Coverage Goals

The test suite aims for:
- **90%+ code coverage** for core business logic
- **100% coverage** for critical paths (scoring, career matching)
- **Edge case coverage** for error scenarios
- **Integration coverage** for all external dependencies

## Continuous Integration

Tests are designed to be:
- **Fast**: Most unit tests complete in <1s
- **Reliable**: Extensive mocking prevents flaky tests
- **Deterministic**: Reproducible results across environments
- **Isolated**: No cross-test dependencies

## Best Practices Demonstrated

1. **Comprehensive Mocking**: All external dependencies are properly mocked
2. **Realistic Data**: Test data mirrors production scenarios
3. **Edge Case Testing**: Boundary conditions and error scenarios
4. **Performance Testing**: Parallel processing and caching validation
5. **State Management**: Proper testing of stateful workflows
6. **Error Propagation**: Verification of error handling across layers

## Future Enhancements

Planned test improvements:
- **Property-based testing** with Hypothesis
- **Load testing** for high-volume scenarios
- **Contract testing** for external service integration
- **Mutation testing** for test quality validation
- **Visual regression testing** for UI components (if applicable)

## Contributing

When adding new features to V4:

1. **Write tests first** (TDD approach)
2. **Follow existing patterns** in test structure
3. **Add realistic fixtures** for new scenarios
4. **Update integration tests** for workflow changes
5. **Maintain high coverage** standards

For questions about the test suite, refer to the test code documentation or the main project README.
