#!/usr/bin/env python3
"""
Test runner for V4 Graph and API tests.
This script provides a convenient way to run all the tests for the v4 implementation.
"""

import pytest
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_unit_tests():
    """Run all unit tests for v4 components."""
    print("Running V4 Unit Tests...")

    test_paths = [
        "tests/unit/graph/v4/nodes/",
        "tests/unit/graph/v4/utils/",
        "tests/unit/graph/v4/",
        "tests/unit/api/v4/",
    ]

    args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker usage
        "--asyncio-mode=auto",  # Auto async mode
    ] + test_paths

    return pytest.main(args)


def run_integration_tests():
    """Run integration tests for v4 workflows."""
    print("Running V4 Integration Tests...")

    args = [
        "-v",
        "--tb=short",
        "--strict-markers",
        "--asyncio-mode=auto",
        "tests/integration/",
    ]

    return pytest.main(args)


def run_all_tests():
    """Run all v4 tests."""
    print("Running All V4 Tests...")

    args = [
        "-v",
        "--tb=short",
        "--strict-markers",
        "--asyncio-mode=auto",
        "tests/",
    ]

    return pytest.main(args)


def run_coverage_tests():
    """Run tests with coverage reporting."""
    print("Running V4 Tests with Coverage...")

    args = [
        "--cov=app.graph.v4",
        "--cov=app.api.routers.v4",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--cov-fail-under=80",
        "-v",
        "--tb=short",
        "--strict-markers",
        "--asyncio-mode=auto",
        "tests/",
    ]

    return pytest.main(args)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run V4 Graph and API tests")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "all", "coverage"],
        default="all",
        help="Type of tests to run (default: all)",
    )

    args = parser.parse_args()

    if args.type == "unit":
        exit_code = run_unit_tests()
    elif args.type == "integration":
        exit_code = run_integration_tests()
    elif args.type == "coverage":
        exit_code = run_coverage_tests()
    else:  # all
        exit_code = run_all_tests()

    sys.exit(exit_code)
