import pytest
from unittest.mock import AsyncMock, patch

from app.graph.v4.nodes.postprocess_career_path import (
    postprocess_career_path,
    process_career_path,
    get_job_progression,
)


class TestPostprocessCareerPath:
    """Test cases for postprocess_career_path node."""

    @pytest.mark.asyncio
    async def test_postprocess_career_path_success(
        self,
        sample_agent_state,
        sample_career_path_data,
        sample_processing_response,
    ):
        """Test successful postprocessing of career path data."""
        state = sample_agent_state.copy()
        state["career_paths"] = [sample_career_path_data]
        state["remain_career_paths"] = [
            {
                "career_path_name": "Data Scientist",
                "group_function": "Information Technology",
            }
        ]

        with patch(
            "app.graph.v4.nodes.postprocess_career_path.process_career_path"
        ) as mock_process:
            mock_process.return_value = sample_processing_response

            result = await postprocess_career_path(state)

            assert "responses" in result
            assert "remain_career_paths" in result
            assert result["responses"] == sample_processing_response
            assert result["remain_career_paths"] == state["remain_career_paths"]

            # Verify process_career_path was called with correct arguments
            mock_process.assert_called_once_with(
                sample_career_path_data,
                state["major"],
                state["university"],
                state["characteristics"],
                state["hobbies"],
                state["skills_gained"],
                state["favourite_subject"],
                state["experience"],
                state["language_level"],
            )

    @pytest.mark.asyncio
    async def test_process_career_path_with_caching(
        self,
        sample_career_path_data,
        sample_processing_response,
    ):
        """Test process_career_path with cache hit."""

        with (
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_set"
            ) as mock_cache_set,
        ):
            # Mock cache hit
            mock_cache_get.return_value = sample_processing_response

            result = await process_career_path(
                sample_career_path_data,
                "Computer Science",
                "Vietnam National University",
                "Analytical",
                "Programming",
                "Python, React",
                "Data Structures",
                "2 years as developer",
                "Advanced English",
            )

            assert result == sample_processing_response
            mock_cache_get.assert_called_once()
            mock_cache_set.assert_not_called()  # Should not set cache on hit

    @pytest.mark.asyncio
    async def test_process_career_path_cache_miss(
        self,
        sample_career_path_data,
        sample_job_progression,
    ):
        """Test process_career_path with cache miss and data processing."""

        with (
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_set"
            ) as mock_cache_set,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_job_progression"
            ) as mock_job_prog,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_comprehensive_salary_data"
            ) as mock_salary,
        ):
            # Mock cache miss
            mock_cache_get.return_value = None
            mock_job_prog.return_value = sample_job_progression
            mock_salary.return_value = {
                "entry_level": {"min": 500, "max": 800},
                "experienced_level": {"min": 800, "max": 1500},
                "senior_level": {"min": 1500, "max": 2500},
            }

            result = await process_career_path(
                sample_career_path_data,
                "Computer Science",
                "Vietnam National University",
                "Analytical",
                "Programming",
                "Python, React",
                "Data Structures",
                "2 years as developer",
                "Advanced English",
            )

            # Verify all expected fields are present
            assert "career_path_id" in result
            assert "career_path" in result
            assert "group_function" in result
            assert "matching_score" in result
            assert "salary" in result
            assert "market_outlook" in result
            assert "statistics" in result

            # Verify caching behavior
            mock_cache_get.assert_called_once()
            mock_cache_set.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_job_progression_success(self):
        """Test successful job progression data retrieval."""
        career_path = "Software Engineer"
        group_function = "Information Technology"

        # Mock Neo4j response with job progression data
        mock_records = [
            {
                "entry_level_jobs": [
                    {
                        "job_level": "entry",
                        "job_title": "Junior Software Engineer",
                        "salary_min": 500,
                        "salary_max": 800,
                        "market_outlook": 1200,
                    }
                ],
                "experienced_level_jobs": [
                    {
                        "job_level": "experienced",
                        "job_title": "Software Engineer",
                        "salary_min": 800,
                        "salary_max": 1500,
                        "market_outlook": 1200,
                    }
                ],
                "senior_level_jobs": [
                    {
                        "job_level": "senior",
                        "job_title": "Senior Software Engineer",
                        "salary_min": 1500,
                        "salary_max": 2500,
                        "market_outlook": 1200,
                    }
                ],
                "manager_level_jobs": [
                    {
                        "job_level": "manager",
                        "job_title": "Engineering Manager",
                        "salary_min": 2000,
                        "salary_max": 3500,
                        "market_outlook": 800,
                    }
                ],
                "director_manager_jobs": [],
                "expert_level_jobs": [
                    {
                        "job_level": "expert",
                        "job_title": "Staff Engineer",
                        "salary_min": 2500,
                        "salary_max": 4000,
                        "market_outlook": 600,
                    }
                ],
                "director_expert_jobs": [],
            }
        ]

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = mock_records

        with patch(
            "app.graph.v4.nodes.postprocess_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_job_progression(career_path, group_function)

            assert "standard" in result
            assert "expert" in result
            assert "manager" in result

            # Verify standard progression (entry, experienced, senior)
            assert len(result["standard"]) == 3
            assert result["standard"][0]["job_title"] == "Junior Software Engineer"
            assert result["standard"][1]["job_title"] == "Software Engineer"
            assert result["standard"][2]["job_title"] == "Senior Software Engineer"

            # Verify manager progression
            assert len(result["manager"]) == 1
            assert result["manager"][0]["job_title"] == "Engineering Manager"

            # Verify expert progression
            assert len(result["expert"]) == 1
            assert result["expert"][0]["job_title"] == "Staff Engineer"

    @pytest.mark.asyncio
    async def test_get_job_progression_no_data(self):
        """Test job progression when no data is found."""
        career_path = "NonExistent Career"
        group_function = "Unknown Function"

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = []

        with patch(
            "app.graph.v4.nodes.postprocess_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_job_progression(career_path, group_function)

            assert result["standard"] == []
            assert result["expert"] == []
            assert result["manager"] == []

    @pytest.mark.asyncio
    async def test_process_career_path_deterministic_statistics(
        self,
        sample_career_path_data,
        sample_job_progression,
    ):
        """Test that statistics generation is deterministic based on input hash."""

        with (
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get,
            patch("app.graph.v4.nodes.postprocess_career_path.cache_set"),
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_job_progression"
            ) as mock_job_prog,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_comprehensive_salary_data"
            ) as mock_salary,
        ):
            mock_cache_get.return_value = None
            mock_job_prog.return_value = sample_job_progression
            mock_salary.return_value = {"entry_level": {"min": 500, "max": 800}}

            # Call with same inputs twice
            result1 = await process_career_path(
                sample_career_path_data,
                "Computer Science",
                "Vietnam National University",
                "Analytical",
                "Programming",
                "Python, React",
                "Data Structures",
                "2 years as developer",
                "Advanced English",
            )

            result2 = await process_career_path(
                sample_career_path_data,
                "Computer Science",
                "Vietnam National University",
                "Analytical",
                "Programming",
                "Python, React",
                "Data Structures",
                "2 years as developer",
                "Advanced English",
            )

            # Statistics should be identical for same inputs
            assert (
                result1["statistics"]["percentage"]
                == result2["statistics"]["percentage"]
            )

    @pytest.mark.asyncio
    async def test_process_career_path_handles_cache_errors(
        self,
        sample_career_path_data,
        sample_job_progression,
    ):
        """Test that cache errors don't break the processing flow."""

        with (
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_set"
            ) as mock_cache_set,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_job_progression"
            ) as mock_job_prog,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_comprehensive_salary_data"
            ) as mock_salary,
        ):
            # Mock cache operations to raise exceptions
            mock_cache_get.side_effect = Exception("Cache get failed")
            mock_cache_set.side_effect = Exception("Cache set failed")
            mock_job_prog.return_value = sample_job_progression
            mock_salary.return_value = {"entry_level": {"min": 500, "max": 800}}

            # Should still process successfully despite cache errors
            result = await process_career_path(
                sample_career_path_data,
                "Computer Science",
                "Vietnam National University",
                "Analytical",
                "Programming",
                "Python, React",
                "Data Structures",
                "2 years as developer",
                "Advanced English",
            )

            assert "career_path" in result
            assert "matching_score" in result

    @pytest.mark.asyncio
    async def test_process_career_path_top_industry_trend(
        self,
        sample_job_progression,
    ):
        """Test top industry trend detection."""
        # Test with a job function that should be in TOP_10_INDUSTRY_TREND
        career_path_data = {
            "career_path": "Software Engineer",
            "matching_score": 85,
            "current_level": "experienced",
            "generic_info": "Great fit",
            "career_path_id": "cp_001",
            "group_function": "Information Technology",
            "description": "Software development",
            "job_function": "Software Development",  # This should be in TOP_10_INDUSTRY_TREND
            "skills_needed": {"technical_skills": [], "soft_skills": []},
            "market_outlook": 1200,
        }

        with (
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get,
            patch("app.graph.v4.nodes.postprocess_career_path.cache_set"),
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_job_progression"
            ) as mock_job_prog,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_comprehensive_salary_data"
            ) as mock_salary,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.TOP_10_INDUSTRY_TREND",
                ["Software Development"],
            ),
        ):
            mock_cache_get.return_value = None
            mock_job_prog.return_value = sample_job_progression
            mock_salary.return_value = {"entry_level": {"min": 500, "max": 800}}

            result = await process_career_path(
                career_path_data,
                "Computer Science",
                "VNU",
                "Analytical",
                "Programming",
                "Python",
                "Algorithms",
                "2 years",
                "English",
            )

            assert result["top_industry_trend"] == 10

    @pytest.mark.asyncio
    async def test_get_job_progression_avoids_duplicates(self):
        """Test that job progression avoids duplicate job titles across progressions."""
        career_path = "Software Engineer"
        group_function = "Information Technology"

        # Mock data with potential duplicates
        mock_records = [
            {
                "entry_level_jobs": [
                    {
                        "job_level": "entry",
                        "job_title": "Software Engineer",  # Same title in multiple levels
                        "salary_min": 500,
                        "salary_max": 800,
                        "market_outlook": 1200,
                    }
                ],
                "experienced_level_jobs": [
                    {
                        "job_level": "experienced",
                        "job_title": "Software Engineer",  # Duplicate
                        "salary_min": 800,
                        "salary_max": 1500,
                        "market_outlook": 1200,
                    }
                ],
                "senior_level_jobs": [],
                "manager_level_jobs": [],
                "director_manager_jobs": [],
                "expert_level_jobs": [],
                "director_expert_jobs": [],
            }
        ]

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = mock_records

        with patch(
            "app.graph.v4.nodes.postprocess_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_job_progression(career_path, group_function)

            # Should only include the job title once
            all_jobs = result["standard"] + result["expert"] + result["manager"]
            job_titles = [job["job_title"] for job in all_jobs]
            assert len(job_titles) == len(set(job_titles))  # No duplicates
