import pytest
from unittest.mock import AsyncMock, patch

from app.graph.v4.nodes.get_career_path import get_career_path


class TestGetCareerPath:
    """Test cases for get_career_path node."""

    @pytest.mark.asyncio
    async def test_get_career_path_with_specific_career_path(
        self,
        sample_agent_state,
        sample_neo4j_job_progression_records,
    ):
        """Test get_career_path when a specific career path is provided."""
        # Setup state with specific career path
        state = sample_agent_state.copy()
        state["career_path"] = "Software Engineer"
        state["group_function"] = "Information Technology"

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = sample_neo4j_job_progression_records

        with (
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_calc_score.return_value = (
                85,
                "experienced",
                "Great fit for your background",
            )

            result = await get_career_path(state)

            assert "career_paths" in result
            assert len(result["career_paths"]) == 1

            career_path = result["career_paths"][0]
            assert career_path["career_path"] == "Software Engineer"
            assert career_path["matching_score"] == 85
            assert career_path["current_level"] == "experienced"
            assert career_path["generic_info"] == "Great fit for your background"
            assert "skills_needed" in career_path
            assert "technical_skills" in career_path["skills_needed"]
            assert "soft_skills" in career_path["skills_needed"]

            # Verify calls
            mock_calc_score.assert_called_once()
            mock_neo4j.execute_query.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_career_path_with_best_match(
        self,
        sample_agent_state,
        sample_career_path_data,
        sample_neo4j_job_progression_records,
    ):
        """Test get_career_path using best_match_career_path from state."""
        state = sample_agent_state.copy()
        state["career_path"] = None
        state["best_match_career_path"] = sample_career_path_data

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = sample_neo4j_job_progression_records

        with patch(
            "app.graph.v4.nodes.get_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_career_path(state)

            assert "career_paths" in result
            assert len(result["career_paths"]) == 1

            career_path = result["career_paths"][0]
            assert career_path["career_path"] == sample_career_path_data["career_path"]
            assert (
                career_path["matching_score"]
                == sample_career_path_data["matching_score"]
            )

    @pytest.mark.asyncio
    async def test_get_career_path_batched_group_function_lookup(
        self,
        sample_agent_state,
        sample_career_path_data,
        sample_neo4j_job_progression_records,
    ):
        """Test that group function lookup is batched for efficiency."""
        state = sample_agent_state.copy()
        state["career_path"] = None
        state["group_function"] = ""  # No initial group function
        state["best_match_career_path"] = sample_career_path_data

        mock_neo4j = AsyncMock()

        # Mock group function lookup response
        group_function_records = [
            {
                "career_path": "Software Engineer",
                "group_function": "Information Technology",
            }
        ]

        # Mock combined query response
        mock_neo4j.execute_query.side_effect = [
            group_function_records,  # First call for group function lookup
            sample_neo4j_job_progression_records,  # Second call for career path data
        ]

        with patch(
            "app.graph.v4.nodes.get_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_career_path(state)

            # Should have made 2 calls: one for group function, one for career data
            assert mock_neo4j.execute_query.call_count == 2
            assert "career_paths" in result

    @pytest.mark.asyncio
    async def test_get_career_path_handles_missing_career_path(
        self,
        sample_agent_state,
    ):
        """Test error handling when career path is not found in Neo4j."""
        state = sample_agent_state.copy()
        state["career_path"] = "NonExistent Career Path"
        state["group_function"] = "Information Technology"

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = []  # No records found

        with (
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_calc_score.return_value = (0, "experienced", "")

            with pytest.raises(ValueError, match="Career path .* not found"):
                await get_career_path(state)

    @pytest.mark.asyncio
    async def test_get_career_path_retries_with_correct_group_function(
        self,
        sample_agent_state,
        sample_neo4j_job_progression_records,
    ):
        """Test that function retries with correct group function if first attempt fails."""
        state = sample_agent_state.copy()
        state["career_path"] = "Software Engineer"
        state["group_function"] = "Wrong Group Function"

        mock_neo4j = AsyncMock()

        # First call returns empty (wrong group function)
        # Second call for group function lookup
        # Third call with correct group function returns data
        mock_neo4j.execute_query.side_effect = [
            [],  # First attempt fails
            [
                {
                    "career_path": "Software Engineer",
                    "group_function": "Information Technology",
                }
            ],  # Group function lookup
            sample_neo4j_job_progression_records,  # Success with correct group function
        ]

        with (
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_calc_score.return_value = (85, "experienced", "Great fit")

            result = await get_career_path(state)

            # Should have made 3 calls: failed attempt, group lookup, successful attempt
            assert mock_neo4j.execute_query.call_count == 3
            assert "career_paths" in result
            assert len(result["career_paths"]) == 1

    @pytest.mark.asyncio
    async def test_get_career_path_processes_skills_correctly(
        self,
        sample_agent_state,
    ):
        """Test that skills are correctly processed and categorized."""
        state = sample_agent_state.copy()
        state["career_path"] = "Software Engineer"
        state["group_function"] = "Information Technology"

        # Mock records with different skill types
        mock_records = [
            {
                "career_path_id": "cp_001",
                "description": "Software development role",
                "job_function": "Software Development",
                "market_outlook": 1200,
                "skill_type": "hard",
                "skills": [
                    {"name": "Python", "description": "Programming language"},
                    {"name": "SQL", "description": "Database queries"},
                    {"name": "React", "description": "Frontend framework"},
                    {"name": "Docker", "description": "Containerization"},
                    {
                        "name": "AWS",
                        "description": "Cloud platform",
                    },  # Should be limited to 4
                ],
            },
            {
                "career_path_id": "cp_001",
                "description": "Software development role",
                "job_function": "Software Development",
                "market_outlook": 1200,
                "skill_type": "soft",
                "skills": [
                    {"name": "Communication", "description": "Team collaboration"},
                    {"name": "Problem Solving", "description": "Analytical thinking"},
                    {"name": "Leadership", "description": "Team guidance"},
                    {"name": "Time Management", "description": "Prioritization"},
                    {
                        "name": "Creativity",
                        "description": "Innovative thinking",
                    },  # Should be limited to 4
                ],
            },
        ]

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = mock_records

        with (
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_calc_score.return_value = (85, "experienced", "Great fit")

            result = await get_career_path(state)

            career_path = result["career_paths"][0]
            skills_needed = career_path["skills_needed"]

            # Should have both technical and soft skills, limited to 4 each
            assert len(skills_needed["technical_skills"]) == 4
            assert len(skills_needed["soft_skills"]) == 4

            # Verify skill structure
            tech_skill = skills_needed["technical_skills"][0]
            assert "name" in tech_skill
            assert "description" in tech_skill

    @pytest.mark.asyncio
    async def test_get_career_path_handles_empty_skills(
        self,
        sample_agent_state,
    ):
        """Test handling when no skills are associated with career path."""
        state = sample_agent_state.copy()
        state["career_path"] = "Software Engineer"
        state["group_function"] = "Information Technology"

        # Mock records with no skills
        mock_records = [
            {
                "career_path_id": "cp_001",
                "description": "Software development role",
                "job_function": "Software Development",
                "market_outlook": 1200,
                "skill_type": None,
                "skills": [],
            }
        ]

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = mock_records

        with (
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_calc_score.return_value = (85, "experienced", "Great fit")

            result = await get_career_path(state)

            career_path = result["career_paths"][0]
            skills_needed = career_path["skills_needed"]

            # Should have empty skill lists
            assert skills_needed["technical_skills"] == []
            assert skills_needed["soft_skills"] == []

    @pytest.mark.asyncio
    async def test_get_career_path_preserves_state_data(
        self,
        sample_agent_state,
        sample_career_path_data,
        sample_neo4j_job_progression_records,
    ):
        """Test that all relevant data from state is preserved in output."""
        state = sample_agent_state.copy()
        state["career_path"] = None
        state["best_match_career_path"] = sample_career_path_data

        mock_neo4j = AsyncMock()
        mock_neo4j.execute_query.return_value = sample_neo4j_job_progression_records

        with patch(
            "app.graph.v4.nodes.get_career_path.get_neo4j_service"
        ) as mock_neo4j_service:
            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j

            result = await get_career_path(state)

            career_path = result["career_paths"][0]

            # Verify all expected fields are present
            expected_fields = [
                "career_path",
                "group_function",
                "career_path_id",
                "description",
                "job_function",
                "market_outlook",
                "matching_score",
                "current_level",
                "generic_info",
                "skills_needed",
            ]

            for field in expected_fields:
                assert field in career_path

            # Verify data integrity
            assert (
                career_path["matching_score"]
                == sample_career_path_data["matching_score"]
            )
            assert (
                career_path["current_level"] == sample_career_path_data["current_level"]
            )
            assert (
                career_path["generic_info"] == sample_career_path_data["generic_info"]
            )
