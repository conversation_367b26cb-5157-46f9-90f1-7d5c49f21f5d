import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import <PERSON><PERSON>lient
from fastapi import FastAPI, status

from app.api.routers.v4.career_path.api import career_path_router_v4
from app.models.suggest_career_path import CareerPathInputV2


@pytest.fixture
def app():
    """Create test FastAPI app with v4 router."""
    app = FastAPI()
    app.include_router(career_path_router_v4)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


class TestCareerPathAPIV4:
    """Test cases for v4 Career Path API endpoints."""

    @pytest.mark.asyncio
    async def test_career_path_suggestion_success(
        self,
        client,
        sample_user_input,
    ):
        """Test successful career path suggestion."""
        request_data = sample_user_input.model_dump()

        # Mock graph response
        mock_graph_response = {
            "responses": {
                "career_path_id": "cp_001",
                "career_path": "Software Engineer",
                "group_function": "Information Technology",
                "job_function": "Software Development",
                "matching_score": 85,
                "generic_info": "Great fit for your background",
                "description": "Develop and maintain software applications",
                "skills_needed": {
                    "technical_skills": [
                        {"name": "Python", "description": "Programming language"}
                    ],
                    "soft_skills": [
                        {"name": "Communication", "description": "Team collaboration"}
                    ],
                },
                "salary": {"entry_level": {"min": 500, "max": 800}},
                "market_outlook": {"number_jobs": 1200},
                "statistics": {
                    "percentage": 25.0,
                    "major": "Computer Science",
                    "career_path": "Software Engineer",
                },
            },
            "remain_career_paths": [
                {
                    "career_path_name": "Data Scientist",
                    "group_function": "Information Technology",
                }
            ],
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.cache_set_with_type"),
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None  # Cache miss
            mock_graph.ainvoke = AsyncMock(return_value=mock_graph_response)

            response = client.post("/internal/v4/suggestion", json=request_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()

            assert response_data["code"] == 200
            assert response_data["error"] is None
            assert "data" in response_data
            assert response_data["data"]["career_path"] == "Software Engineer"
            assert response_data["data"]["matching_score"] == 85
            assert "remain_career_paths" in response_data["data"]

            # Verify graph was called
            mock_graph.ainvoke.assert_called_once()

    @pytest.mark.asyncio
    async def test_career_path_suggestion_cache_hit(
        self,
        client,
        sample_user_input,
    ):
        """Test career path suggestion with cache hit."""
        request_data = sample_user_input.model_dump()

        cached_data = {
            "career_path": "Software Engineer",
            "matching_score": 85,
            "remain_career_paths": [],
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = cached_data
            mock_graph.ainvoke = AsyncMock()

            response = client.post("/internal/v4/suggestion", json=request_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()

            assert response_data["code"] == 200
            assert response_data["data"] == cached_data

            # Verify graph was not called due to cache hit
            mock_graph.ainvoke.assert_not_called()

    @pytest.mark.asyncio
    async def test_career_path_suggestion_error_handling(
        self,
        client,
        sample_user_input,
    ):
        """Test error handling in career path suggestion."""
        request_data = sample_user_input.model_dump()

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None
            mock_graph.ainvoke = AsyncMock(
                side_effect=Exception("Graph execution failed")
            )

            response = client.post("/internal/v4/suggestion", json=request_data)

            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            response_data = response.json()

            assert response_data["code"] == 500
            assert "Graph execution failed" in response_data["error"]
            assert response_data["data"] is None

    def test_career_path_suggestion_invalid_input(self, client):
        """Test career path suggestion with invalid input data."""
        invalid_data = {
            "major": "",  # Empty required field
            "experience": "2 years",
            # Missing other required fields
        }

        response = client.post("/internal/v4/suggestion", json=invalid_data)

        # Should return validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_detailed_info_success(
        self,
        client,
        sample_user_input,
    ):
        """Test successful detailed info retrieval."""
        request_data = sample_user_input.model_dump()
        request_data["career_path"] = "Software Engineer"

        mock_detailed_response = {
            "detailed_info": "## Why You're a Great Fit\n\nBased on your background...",
            "matching_score": 85,
            "analysis": "Detailed analysis of fit",
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.cache_set_with_type"),
            patch(
                "app.api.routers.v4.career_path.api.calculate_matching_score"
            ) as mock_calc_score,
            patch(
                "app.api.routers.v4.career_path.api.get_detailed_info"
            ) as mock_detailed_info,
        ):
            mock_cache_get.return_value = None  # Cache miss
            mock_calc_score.return_value = (85, "experienced", "Great fit")
            mock_detailed_info.return_value = mock_detailed_response

            response = client.post("/internal/v4/detailed-info", json=request_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()

            assert response_data["code"] == 200
            assert response_data["error"] is None
            assert response_data["data"] == mock_detailed_response

            # Verify functions were called
            mock_calc_score.assert_called_once()
            mock_detailed_info.assert_called_once()

    @pytest.mark.asyncio
    async def test_detailed_info_cache_hit(
        self,
        client,
        sample_user_input,
    ):
        """Test detailed info with cache hit."""
        request_data = sample_user_input.model_dump()
        request_data["career_path"] = "Software Engineer"

        cached_data = {
            "detailed_info": "Cached detailed info",
            "matching_score": 85,
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch(
                "app.api.routers.v4.career_path.api.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_cache_get.return_value = cached_data
            mock_calc_score.return_value = (85, "experienced", "Great fit")

            response = client.post("/internal/v4/detailed-info", json=request_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()

            assert response_data["data"] == cached_data

            # Verify matching score calculation was not called due to cache hit
            mock_calc_score.assert_not_called()

    @pytest.mark.asyncio
    async def test_detailed_info_user_profile_formatting(
        self,
        client,
        sample_user_input,
    ):
        """Test that user profile is correctly formatted for detailed info."""
        request_data = sample_user_input.model_dump()
        request_data["career_path"] = "Software Engineer"

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch(
                "app.api.routers.v4.career_path.api.calculate_matching_score"
            ) as mock_calc_score,
            patch(
                "app.api.routers.v4.career_path.api.get_detailed_info"
            ) as mock_detailed_info,
        ):
            mock_cache_get.return_value = None
            mock_calc_score.return_value = (85, "experienced", "Great fit")
            mock_detailed_info.return_value = {"detailed_info": "Test"}

            client.post("/internal/v4/detailed-info", json=request_data)

            # Verify the user profile was formatted correctly
            call_args = mock_detailed_info.call_args
            user_profile = call_args[0][1]  # Second argument is user_profile

            assert "Ngành học:" in user_profile
            assert "Trường đại học:" in user_profile
            assert "Tính cách:" in user_profile
            assert "Sở thích:" in user_profile
            assert "Kỹ năng :" in user_profile
            assert "Môn học yêu thích:" in user_profile
            assert "Kinh nghiệm làm việc:" in user_profile
            assert "Trình độ ngoại ngữ:" in user_profile

    @pytest.mark.asyncio
    async def test_detailed_info_error_handling(
        self,
        client,
        sample_user_input,
    ):
        """Test error handling in detailed info endpoint."""
        request_data = sample_user_input.model_dump()
        request_data["career_path"] = "Software Engineer"

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch(
                "app.api.routers.v4.career_path.api.calculate_matching_score"
            ) as mock_calc_score,
        ):
            mock_cache_get.return_value = None
            mock_calc_score.side_effect = Exception("Calculation failed")

            response = client.post("/internal/v4/detailed-info", json=request_data)

            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            response_data = response.json()

            assert response_data["code"] == 500
            assert "Calculation failed" in response_data["error"]

    def test_detailed_info_missing_career_path(self, client, sample_user_input):
        """Test detailed info endpoint without career_path."""
        request_data = sample_user_input.model_dump()
        request_data["career_path"] = None

        response = client.post("/internal/v4/detailed-info", json=request_data)

        # Should still process but might have different behavior
        # The actual behavior depends on how the endpoint handles None career_path
        assert response.status_code in [200, 400, 422, 500]

    @pytest.mark.asyncio
    async def test_cache_key_generation(
        self,
        client,
        sample_user_input,
    ):
        """Test that cache keys are generated correctly."""
        request_data = sample_user_input.model_dump()

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch(
                "app.api.routers.v4.career_path.api.generate_cache_key"
            ) as mock_generate_key,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None
            mock_generate_key.return_value = "test_cache_key"
            mock_graph.ainvoke = AsyncMock(
                return_value={"responses": {}, "remain_career_paths": []}
            )

            client.post("/internal/v4/suggestion", json=request_data)

            # Verify cache key was generated
            mock_generate_key.assert_called_once_with("suggestion_v4", request_data)

    @pytest.mark.asyncio
    async def test_langfuse_integration(
        self,
        client,
        sample_user_input,
    ):
        """Test that Langfuse handler is properly configured."""
        request_data = sample_user_input.model_dump()

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
            patch(
                "app.api.routers.v4.career_path.api.langfuse_handler"
            ) as mock_langfuse,
        ):
            mock_cache_get.return_value = None
            mock_graph.ainvoke = AsyncMock(
                return_value={"responses": {}, "remain_career_paths": []}
            )

            client.post("/internal/v4/suggestion", json=request_data)

            # Verify graph was called with Langfuse configuration
            call_args = mock_graph.ainvoke.call_args
            config = call_args[1]["config"]

            assert "callbacks" in config
            assert mock_langfuse in config["callbacks"]
            assert config["run_name"] == "Career Path Suggestion V4"

    def test_pydantic_model_validation(self):
        """Test Pydantic model validation for CareerPathInputV2."""
        # Test valid data
        valid_data = {
            "major": "Computer Science",
            "experience": "2 years",
            "skills_gained": "Python, React",
            "university": "VNU",
            "language_level": "Advanced",
            "number_output": 8,
            "hobbies": "Programming",
            "characteristics": "Analytical",
            "favourite_subject": "Algorithms",
        }

        model = CareerPathInputV2(**valid_data)
        assert model.major == "Computer Science"
        assert model.number_output == 8  # Default value

        # Test with custom number_output
        valid_data["number_output"] = 5
        model = CareerPathInputV2(**valid_data)
        assert model.number_output == 5

        # Test with optional fields
        valid_data["career_path"] = "Software Engineer"
        valid_data["group_function"] = "Information Technology"
        model = CareerPathInputV2(**valid_data)
        assert model.career_path == "Software Engineer"
        assert model.group_function == "Information Technology"

    def test_response_format_consistency(self, client, sample_user_input):
        """Test that API responses follow consistent format."""
        request_data = sample_user_input.model_dump()

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None
            mock_graph.ainvoke = AsyncMock(
                return_value={"responses": {"test": "data"}, "remain_career_paths": []}
            )

            response = client.post("/internal/v4/suggestion", json=request_data)

            assert response.status_code == 200
            response_data = response.json()

            # Verify response structure
            required_fields = ["code", "error", "data", "metadata"]
            for field in required_fields:
                assert field in response_data

            assert response_data["code"] == 200
            assert response_data["error"] is None
            assert isinstance(response_data["metadata"], dict)
