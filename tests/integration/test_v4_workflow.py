from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.api.routers.v4.career_path.api import (
    career_path_detailed_info,
    career_path_suggestion,
)
from app.graph.v4.graph import graph


class TestV4WorkflowIntegration:
    """Integration tests for the complete v4 workflow."""

    @pytest.mark.asyncio
    async def test_end_to_end_career_suggestion_workflow(
        self,
        sample_user_input,
    ):
        """Test complete end-to-end career suggestion workflow."""

        # Mock external dependencies
        mock_neo4j_career_records = [
            {
                "career_path": "Software Engineer",
                "group_function": "Information Technology",
            },
            {
                "career_path": "Data Scientist",
                "group_function": "Information Technology",
            },
            {
                "career_path": "Product Manager",
                "group_function": "Business Development",
            },
        ]

        mock_neo4j_job_records = [
            {
                "career_path_id": "cp_001",
                "description": "Develop and maintain software applications",
                "job_function": "Software Development",
                "market_outlook": 1200,
                "skill_type": "hard",
                "skills": [
                    {"name": "Python", "description": "Programming language"},
                    {"name": "SQL", "description": "Database queries"},
                ],
            },
            {
                "career_path_id": "cp_001",
                "description": "Develop and maintain software applications",
                "job_function": "Software Development",
                "market_outlook": 1200,
                "skill_type": "soft",
                "skills": [
                    {"name": "Communication", "description": "Team collaboration"},
                    {"name": "Problem Solving", "description": "Analytical thinking"},
                ],
            },
        ]

        mock_job_progression_records = [
            {
                "entry_level_jobs": [
                    {
                        "job_level": "entry",
                        "job_title": "Junior Software Engineer",
                        "salary_min": 500,
                        "salary_max": 800,
                        "market_outlook": 1200,
                    }
                ],
                "experienced_level_jobs": [
                    {
                        "job_level": "experienced",
                        "job_title": "Software Engineer",
                        "salary_min": 800,
                        "salary_max": 1500,
                        "market_outlook": 1200,
                    }
                ],
                "senior_level_jobs": [
                    {
                        "job_level": "senior",
                        "job_title": "Senior Software Engineer",
                        "salary_min": 1500,
                        "salary_max": 2500,
                        "market_outlook": 1200,
                    }
                ],
                "manager_level_jobs": [],
                "director_manager_jobs": [],
                "expert_level_jobs": [],
                "director_expert_jobs": [],
            }
        ]

        # Mock LLM responses
        from app.graph.v4.models.responses import BestCareerPaths, SuggestGroupFunction

        mock_group_function_response = SuggestGroupFunction(
            major_based_group=["Information Technology"],
            experience_based_group=["Information Technology"],
        )

        mock_career_paths_response = BestCareerPaths(
            career_paths=["Software Engineer", "Data Scientist"]
        )

        # Mock evaluation chains
        mock_evaluation_section = MagicMock()
        mock_evaluation_section.evaluation = [
            MagicMock(criterion="Major", score=9),
            MagicMock(criterion="Experience", score=8),
            MagicMock(criterion="Skills gained", score=8),
            MagicMock(criterion="University", score=7),
            MagicMock(criterion="Favourite subject", score=9),
            MagicMock(criterion="Hobbies", score=6),
            MagicMock(criterion="Characteristics", score=8),
            MagicMock(criterion="Language level", score=7),
        ]

        mock_experience_section = MagicMock()
        mock_experience_section.experience_segment = "experienced_level"

        mock_current_level_section = MagicMock()
        mock_current_level_section.current_level = MagicMock()
        mock_current_level_section.current_level.value = "experienced"

        with (
            patch(
                "app.graph.v4.nodes.suggest_career_path.get_neo4j_service"
            ) as mock_neo4j_service,
            patch(
                "app.graph.v4.nodes.get_career_path.get_neo4j_service"
            ) as mock_neo4j_service2,
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_neo4j_service"
            ) as mock_neo4j_service3,
            patch("app.graph.v4.nodes.suggest_career_path.get_chat_openai") as mock_llm,
            patch(
                "app.graph.v4.utils.calculations.evaluation_chain"
            ) as mock_eval_chain,
            patch(
                "app.graph.v4.utils.calculations.experience_analysis_chain"
            ) as mock_exp_chain,
            patch(
                "app.graph.v4.utils.calculations.current_level_chain"
            ) as mock_level_chain,
            patch(
                "app.graph.v4.utils.calculations.get_generic_info"
            ) as mock_generic_info,
            patch("app.graph.v4.utils.calculations.cache_get") as mock_cache_get,
            patch("app.graph.v4.utils.calculations.cache_set"),
            patch(
                "app.graph.v4.nodes.postprocess_career_path.cache_get"
            ) as mock_cache_get2,
            patch("app.graph.v4.nodes.postprocess_career_path.cache_set"),
            patch(
                "app.graph.v4.nodes.postprocess_career_path.get_comprehensive_salary_data"
            ) as mock_salary,
        ):
            # Setup Neo4j mocks
            mock_neo4j = AsyncMock()
            mock_neo4j.execute_query.side_effect = [
                mock_neo4j_career_records,  # For suggest_career_path
                mock_neo4j_career_records,  # For get_career_path group function lookup
                mock_neo4j_job_records,  # For get_career_path main query
                mock_job_progression_records,  # For postprocess_career_path
            ]

            mock_neo4j_service.return_value.__aenter__.return_value = mock_neo4j
            mock_neo4j_service2.return_value.__aenter__.return_value = mock_neo4j
            mock_neo4j_service3.return_value.__aenter__.return_value = mock_neo4j

            # Setup LLM mocks
            mock_suggest_group_chain = AsyncMock()
            mock_suggest_group_chain.ainvoke.return_value = mock_group_function_response

            mock_suggest_career_chain = AsyncMock()
            mock_suggest_career_chain.ainvoke.return_value = mock_career_paths_response

            mock_llm.return_value.with_structured_output.side_effect = [
                mock_suggest_group_chain,
                mock_suggest_career_chain,
            ]

            # Setup evaluation chain mocks
            mock_eval_chain.ainvoke.return_value = mock_evaluation_section
            mock_exp_chain.ainvoke.return_value = mock_experience_section
            mock_level_chain.ainvoke.return_value = mock_current_level_section
            mock_generic_info.return_value = "Great fit for your technical background"

            # Setup cache mocks (cache miss)
            mock_cache_get.return_value = None
            mock_cache_get2.return_value = None

            # Setup salary mock
            mock_salary.return_value = {
                "entry_level": {"min": 500, "max": 800},
                "experienced_level": {"min": 800, "max": 1500},
                "senior_level": {"min": 1500, "max": 2500},
            }

            # Execute the complete workflow
            input_data = sample_user_input.model_dump()
            input_data["career_path"] = None  # Trigger suggestion workflow

            result = await graph.ainvoke(input_data)

            # Verify the complete workflow output
            assert "responses" in result
            assert "remain_career_paths" in result

            responses = result["responses"]
            assert "career_path_id" in responses
            assert "career_path" in responses
            assert "matching_score" in responses
            assert "skills_needed" in responses
            assert "salary" in responses
            assert "market_outlook" in responses

            # Verify matching score calculation worked
            assert isinstance(responses["matching_score"], int)
            assert 0 <= responses["matching_score"] <= 100

            # Verify skills are properly structured
            skills_needed = responses["skills_needed"]
            assert "technical_skills" in skills_needed
            assert "soft_skills" in skills_needed
            assert isinstance(skills_needed["technical_skills"], list)
            assert isinstance(skills_needed["soft_skills"], list)

    @pytest.mark.asyncio
    async def test_api_to_graph_integration(
        self,
        sample_user_input,
    ):
        """Test integration between API layer and graph execution."""

        # Mock the graph execution
        mock_graph_response = {
            "responses": {
                "career_path_id": "cp_001",
                "career_path": "Software Engineer",
                "group_function": "Information Technology",
                "job_function": "Software Development",
                "matching_score": 85,
                "generic_info": "Great fit for your background",
                "description": "Develop and maintain software applications",
                "skills_needed": {
                    "technical_skills": [
                        {"name": "Python", "description": "Programming language"}
                    ],
                    "soft_skills": [
                        {"name": "Communication", "description": "Team collaboration"}
                    ],
                },
                "salary": {"entry_level": {"min": 500, "max": 800}},
                "market_outlook": {"number_jobs": 1200},
                "statistics": {
                    "percentage": 25.0,
                    "major": "Computer Science",
                    "career_path": "Software Engineer",
                },
            },
            "remain_career_paths": [
                {
                    "career_path_name": "Data Scientist",
                    "group_function": "Information Technology",
                }
            ],
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.cache_set_with_type"),
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None  # Cache miss
            mock_graph.ainvoke = AsyncMock(return_value=mock_graph_response)

            # Call the API function directly
            result = await career_path_suggestion(sample_user_input)

            # Verify API response structure
            assert result.status_code == 200
            response_data = result.body.decode()
            import json

            response_json = json.loads(response_data)

            assert response_json["code"] == 200
            assert response_json["error"] is None
            assert "data" in response_json

            # Verify data structure matches expected format
            data = response_json["data"]
            assert data["career_path"] == "Software Engineer"
            assert data["matching_score"] == 85
            assert "remain_career_paths" in data

            # Verify graph was called with correct configuration
            mock_graph.ainvoke.assert_called_once()
            call_args = mock_graph.ainvoke.call_args

            # Verify input data was passed correctly
            assert call_args[0][0]["major"] == sample_user_input.major
            assert call_args[0][0]["experience"] == sample_user_input.experience

            # Verify Langfuse configuration
            config = call_args[1]["config"]
            assert "callbacks" in config
            assert config["run_name"] == "Career Path Suggestion V4"

    @pytest.mark.asyncio
    async def test_detailed_info_integration(
        self,
        sample_user_input,
    ):
        """Test integration of detailed info endpoint with calculation utilities."""

        sample_user_input.career_path = "Software Engineer"

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.cache_set_with_type"),
            patch(
                "app.api.routers.v4.career_path.api.calculate_matching_score"
            ) as mock_calc_score,
            patch(
                "app.api.routers.v4.career_path.api.get_detailed_info"
            ) as mock_detailed_info,
        ):
            mock_cache_get.return_value = None  # Cache miss
            mock_calc_score.return_value = (
                85,
                "experienced",
                "Great fit for your background",
            )
            mock_detailed_info.return_value = {
                "detailed_info": "## Why You're a Great Fit\n\nBased on your background in Computer Science...",
                "matching_score": 85,
                "analysis": "Your technical skills align well with this role",
            }

            result = await career_path_detailed_info(sample_user_input)

            # Verify API response
            assert result.status_code == 200
            response_data = result.body.decode()
            import json

            response_json = json.loads(response_data)

            assert response_json["code"] == 200
            assert response_json["error"] is None

            data = response_json["data"]
            assert "detailed_info" in data
            assert data["matching_score"] == 85

            # Verify the integration flow
            mock_calc_score.assert_called_once()
            mock_detailed_info.assert_called_once()

            # Verify user profile was correctly formatted and passed
            calc_call_args = mock_calc_score.call_args
            user_profile = calc_call_args[0][1]
            assert "Ngành học: Computer Science" in user_profile
            assert "Kinh nghiệm làm việc:" in user_profile

    @pytest.mark.asyncio
    async def test_error_propagation_through_workflow(
        self,
        sample_user_input,
    ):
        """Test that errors are properly propagated through the workflow layers."""

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None

            # Simulate a graph execution error
            mock_graph.ainvoke = AsyncMock(
                side_effect=Exception("Neo4j connection failed")
            )

            result = await career_path_suggestion(sample_user_input)

            # Verify error is properly handled at API level
            assert result.status_code == 500
            response_data = result.body.decode()
            import json

            response_json = json.loads(response_data)

            assert response_json["code"] == 500
            assert "Neo4j connection failed" in response_json["error"]
            assert response_json["data"] is None

    @pytest.mark.asyncio
    async def test_caching_integration_across_layers(
        self,
        sample_user_input,
    ):
        """Test that caching works correctly across different layers."""

        cached_result = {
            "career_path": "Software Engineer",
            "matching_score": 85,
            "remain_career_paths": [],
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            # First call - cache miss
            mock_cache_get.side_effect = [None, cached_result]  # First miss, then hit
            mock_graph.ainvoke = AsyncMock(
                return_value={
                    "responses": cached_result,
                    "remain_career_paths": [],
                }
            )

            # First API call should execute graph
            result1 = await career_path_suggestion(sample_user_input)
            assert result1.status_code == 200
            mock_graph.ainvoke.assert_called_once()

            # Reset the mock for second call
            mock_graph.ainvoke.reset_mock()

            # Second API call should hit cache
            result2 = await career_path_suggestion(sample_user_input)
            assert result2.status_code == 200
            mock_graph.ainvoke.assert_not_called()  # Should not call graph due to cache hit

    @pytest.mark.asyncio
    async def test_workflow_routing_logic(
        self,
        sample_user_input,
    ):
        """Test that workflow routing logic works correctly for different input scenarios."""

        # Test scenario 1: No career_path provided - should go through suggest_career_path
        input_data_no_career = sample_user_input.model_dump()
        input_data_no_career["career_path"] = None

        # Test scenario 2: Career path provided - should go directly to get_career_path
        input_data_with_career = sample_user_input.model_dump()
        input_data_with_career["career_path"] = "Software Engineer"

        # We'll test the routing logic by checking which nodes would be called
        # Import the routing function
        from app.graph.v4.graph import suggest_or_generate
        from app.graph.v4.state import AgentState

        # Test routing for no career path
        state_no_career = AgentState(**input_data_no_career)
        route_no_career = suggest_or_generate(state_no_career)
        assert route_no_career == "suggest_career_path"

        # Test routing for existing career path
        state_with_career = AgentState(**input_data_with_career)
        route_with_career = suggest_or_generate(state_with_career)
        assert route_with_career == "get_career_path"

        # Test edge case: empty string career path
        input_data_empty_career = sample_user_input.model_dump()
        input_data_empty_career["career_path"] = ""
        state_empty_career = AgentState(**input_data_empty_career)
        route_empty_career = suggest_or_generate(state_empty_career)
        assert route_empty_career == "suggest_career_path"

    @pytest.mark.asyncio
    async def test_data_format_consistency_across_layers(
        self,
        sample_user_input,
    ):
        """Test that data formats remain consistent across API and graph layers."""

        # Test with both API endpoint and direct graph execution
        mock_graph_response = {
            "responses": {
                "career_path": "Software Engineer",
                "matching_score": 85,
                "skills_needed": {
                    "technical_skills": [
                        {"name": "Python", "description": "Programming language"}
                    ],
                    "soft_skills": [
                        {"name": "Communication", "description": "Team collaboration"}
                    ],
                },
            },
            "remain_career_paths": [
                {
                    "career_path_name": "Data Scientist",
                    "group_function": "Information Technology",
                }
            ],
        }

        with (
            patch(
                "app.api.routers.v4.career_path.api.cache_get_with_type"
            ) as mock_cache_get,
            patch("app.api.routers.v4.career_path.api.suggest_graph") as mock_graph,
        ):
            mock_cache_get.return_value = None
            mock_graph.ainvoke = AsyncMock(return_value=mock_graph_response)

            # Call API endpoint
            api_result = await career_path_suggestion(sample_user_input)

            # Parse API response
            import json

            api_data = json.loads(api_result.body.decode())["data"]

            # Verify data structure consistency
            assert (
                api_data["career_path"]
                == mock_graph_response["responses"]["career_path"]
            )
            assert (
                api_data["matching_score"]
                == mock_graph_response["responses"]["matching_score"]
            )
            assert "skills_needed" in api_data
            assert "remain_career_paths" in api_data

            # Verify skills structure is preserved
            api_skills = api_data["skills_needed"]
            graph_skills = mock_graph_response["responses"]["skills_needed"]
            assert api_skills["technical_skills"] == graph_skills["technical_skills"]
            assert api_skills["soft_skills"] == graph_skills["soft_skills"]
