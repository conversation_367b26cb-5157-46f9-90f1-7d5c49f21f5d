import asyncio
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.graph.v4.models.enums import Criterion, JobLevel
from app.graph.v4.models.responses import (
    BestCareerPaths,
    Evaluation,
    EvaluationResult,
    SuggestGroupFunction,
)
from app.models.suggest_career_path import CareerPathInputV2

# Set the default loop scope
pytest_plugins = ("pytest_asyncio",)


# If you need custom event loop configuration, use the policy fixture
@pytest.fixture(scope="session")
def event_loop_policy():
    return asyncio.DefaultEventLoopPolicy()


@pytest.fixture
def sample_user_input() -> CareerPathInputV2:
    """Sample user input for testing."""
    return CareerPathInputV2(
        career_path=None,
        group_function=None,
        major="Computer Science",
        experience="2 years as Software Developer",
        skills_gained="Python, FastAPI, React",
        university="Vietnam National University",
        language_level="Advanced English",
        hobbies="Programming, Reading tech blogs",
        characteristics="Analytical, Problem-solver",
        favourite_subject="Data Structures and Algorithms",
        number_output=8,
    )


@pytest.fixture
def sample_user_profile() -> str:
    """Sample formatted user profile string."""
    return """Ngành học: Computer Science
Trường đại học: Vietnam National University
Tính cách: Analytical, Problem-solver
Sở thích: Programming, Reading tech blogs
Kỹ năng : Python, FastAPI, React
Môn học yêu thích: Data Structures and Algorithms
Kinh nghiệm làm việc: 2 years as Software Developer
Trình độ ngoại ngữ: Advanced English"""


@pytest.fixture
def sample_career_path_data() -> Dict[str, Any]:
    """Sample career path data for testing."""
    return {
        "career_path": "Software Engineer",
        "matching_score": 85,
        "current_level": "experienced",
        "generic_info": "Great fit for your technical background",
        "career_path_id": "cp_001",
        "group_function": "Information Technology",
        "description": "Develop and maintain software applications",
        "job_function": "Software Development",
        "skills_needed": {
            "technical_skills": [
                {"name": "Python", "description": "Programming language"},
                {"name": "SQL", "description": "Database queries"},
            ],
            "soft_skills": [
                {"name": "Communication", "description": "Team collaboration"},
                {"name": "Problem Solving", "description": "Analytical thinking"},
            ],
        },
        "market_outlook": 1200,
    }


@pytest.fixture
def sample_evaluation_result() -> EvaluationResult:
    """Sample evaluation result for testing."""
    evaluations = [
        Evaluation(criterion=Criterion.MAJOR, score=9),
        Evaluation(criterion=Criterion.EXPERIENCE, score=8),
        Evaluation(criterion=Criterion.SKILLS_GAINED, score=8),
        Evaluation(criterion=Criterion.UNIVERSITY, score=7),
        Evaluation(criterion=Criterion.FAVOURITE_SUBJECT, score=9),
        Evaluation(criterion=Criterion.HOBBIES, score=6),
        Evaluation(criterion=Criterion.CHARACTERISTICS, score=8),
        Evaluation(criterion=Criterion.LANGUAGE_LEVEL, score=7),
    ]

    return EvaluationResult(
        evaluation=evaluations,
        current_level=JobLevel["Experienced"],
        has_experience=True,
        experience_segment="experienced_level",
        time_period="2 years",
    )


@pytest.fixture
def sample_neo4j_career_path_records() -> List[Dict[str, Any]]:
    """Sample Neo4j records for career path data."""
    return [
        {
            "career_path": "Software Engineer",
            "group_function": "Information Technology",
        },
        {
            "career_path": "Data Scientist",
            "group_function": "Information Technology",
        },
        {
            "career_path": "Product Manager",
            "group_function": "Business Development",
        },
    ]


@pytest.fixture
def sample_neo4j_job_progression_records() -> List[Dict[str, Any]]:
    """Sample Neo4j records for job progression data."""
    return [
        {
            "career_path_id": "cp_001",
            "description": "Develop and maintain software applications",
            "job_function": "Software Development",
            "market_outlook": 1200,
            "skill_type": "hard",
            "skills": [
                {"name": "Python", "description": "Programming language"},
                {"name": "SQL", "description": "Database queries"},
            ],
        },
        {
            "career_path_id": "cp_001",
            "description": "Develop and maintain software applications",
            "job_function": "Software Development",
            "market_outlook": 1200,
            "skill_type": "soft",
            "skills": [
                {"name": "Communication", "description": "Team collaboration"},
                {"name": "Problem Solving", "description": "Analytical thinking"},
            ],
        },
    ]


@pytest.fixture
def sample_suggest_group_function_response() -> SuggestGroupFunction:
    """Sample group function suggestion response."""
    return SuggestGroupFunction(
        major_based_group=["Information Technology", "Engineering"],
        experience_based_group=["Information Technology"],
    )


@pytest.fixture
def sample_best_career_paths_response() -> BestCareerPaths:
    """Sample best career paths response."""
    return BestCareerPaths(
        career_paths=[
            "Software Engineer",
            "Data Scientist",
            "Product Manager",
            "DevOps Engineer",
        ]
    )


@pytest.fixture
def mock_neo4j_service():
    """Mock Neo4j service."""
    mock_service = AsyncMock()
    mock_service.__aenter__ = AsyncMock(return_value=mock_service)
    mock_service.__aexit__ = AsyncMock(return_value=None)
    mock_service.execute_query = AsyncMock()
    return mock_service


@pytest.fixture
def mock_llm_chain():
    """Mock LLM chain."""
    mock_chain = AsyncMock()
    mock_chain.ainvoke = AsyncMock()
    mock_chain.with_structured_output = MagicMock(return_value=mock_chain)
    return mock_chain


@pytest.fixture
def mock_cache_service():
    """Mock cache service."""
    mock_cache = AsyncMock()
    mock_cache.get = AsyncMock(return_value=None)
    mock_cache.set = AsyncMock()
    return mock_cache


@pytest.fixture
def sample_agent_state() -> Dict[str, Any]:
    """Sample agent state for graph testing."""
    return {
        "career_path": None,
        "major": "Computer Science",
        "university": "Vietnam National University",
        "skills_gained": "Python, FastAPI, React",
        "experience": "2 years as Software Developer",
        "language_level": "Advanced English",
        "hobbies": "Programming, Reading tech blogs",
        "characteristics": "Analytical, Problem-solver",
        "favourite_subject": "Data Structures and Algorithms",
        "number_output": 8,
        "group_function": "Information Technology",
        "career_paths": [],
        "best_match_career_path": {},
        "remain_career_paths": [],
        "responses": [],
    }


@pytest.fixture
def sample_processing_response() -> Dict[str, Any]:
    """Sample career path processing response."""
    return {
        "career_path_id": "cp_001",
        "career_path": "Software Engineer",
        "group_function": "Information Technology",
        "job_function": "Software Development",
        "matching_score": 85,
        "generic_info": "Great fit for your technical background",
        "top_industry_trend": 10,
        "description": "Develop and maintain software applications",
        "skills_needed": {
            "technical_skills": [
                {"name": "Python", "description": "Programming language"},
                {"name": "SQL", "description": "Database queries"},
            ],
            "soft_skills": [
                {"name": "Communication", "description": "Team collaboration"},
                {"name": "Problem Solving", "description": "Analytical thinking"},
            ],
        },
        "salary": {
            "entry_level": {"min": 500, "max": 800},
            "experienced_level": {"min": 800, "max": 1500},
            "senior_level": {"min": 1500, "max": 2500},
        },
        "market_outlook": {"number_jobs": 1200},
        "statistics": {
            "percentage": 25.0,
            "major": "Computer Science",
            "career_path": "Software Engineer",
        },
    }


@pytest.fixture
def sample_job_progression() -> Dict[str, List[Dict[str, Any]]]:
    """Sample job progression data."""
    return {
        "standard": [
            {
                "job_level": "entry",
                "job_title": "Junior Software Engineer",
                "salary_min": 500,
                "salary_max": 800,
                "market_outlook": 1200,
            },
            {
                "job_level": "experienced",
                "job_title": "Software Engineer",
                "salary_min": 800,
                "salary_max": 1500,
                "market_outlook": 1200,
            },
        ],
        "expert": [
            {
                "job_level": "expert",
                "job_title": "Senior Software Engineer",
                "salary_min": 1500,
                "salary_max": 2500,
                "market_outlook": 1200,
            }
        ],
        "manager": [
            {
                "job_level": "manager",
                "job_title": "Engineering Manager",
                "salary_min": 2000,
                "salary_max": 3500,
                "market_outlook": 800,
            }
        ],
    }
