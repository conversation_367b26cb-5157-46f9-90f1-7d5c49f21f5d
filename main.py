import logging
import os
from contextlib import asynccontextmanager

import sentry_sdk
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

from app.api.routers.health import health_router
from app.api.routers.v1.career_path.search import career_path_search_router
from app.api.routers.v1.career_path.suggestion import career_path_router
from app.api.routers.v2.career_path.api import career_path_router_v2
from app.api.routers.v3.career_path.api import career_path_router_v3
from app.api.routers.v4.career_path.api import career_path_router_v4
from app.core.constants import LOGGING_CONFIG
from app.core.settings import get_settings

settings = get_settings()

os.makedirs("logs", exist_ok=True)
logging.config.dictConfig(LOGGING_CONFIG)

logger = logging.getLogger(__name__)

sentry_sdk.init(
    dsn=settings.sentry_dsn,
    traces_sample_rate=0.1,
    integrations=[
        StarletteIntegration(),
        FastApiIntegration(),
    ],
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown."""
    # Startup
    logger.info("Starting up application...")
    yield
    # Shutdown
    logger.info("Shutting down application...")

    # Clean up LLM HTTP clients
    from app.services.llm import cleanup_http_clients

    await cleanup_http_clients()
    logger.info("LLM HTTP clients cleaned up")

    # Clean up MongoDB connection
    from app.services.mongodb import cleanup_mongodb

    await cleanup_mongodb()
    logger.info("MongoDB connection closed")

    # Clean up Redis connection
    from app.services.redis import cleanup_redis

    await cleanup_redis()
    logger.info("Redis connection closed")

    # Clean up Neo4j connection
    from app.services.neo4j import cleanup_neo4j

    await cleanup_neo4j()
    logger.info("Neo4j connection closed")


app = FastAPI(lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


app.include_router(health_router)
app.include_router(career_path_router)
app.include_router(career_path_search_router)
app.include_router(career_path_router_v2)
app.include_router(career_path_router_v3)
app.include_router(career_path_router_v4)


if __name__ == "__main__":
    reload = True if settings.app_env == "dev" else False

    uvicorn.run(
        app="main:app",
        host=settings.app_host,
        port=settings.app_port,
        log_config=LOGGING_CONFIG,
        reload=reload,
    )
