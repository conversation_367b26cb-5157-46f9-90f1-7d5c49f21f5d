[project]
name = "upzi-career-path"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12,<3.14"
dependencies = [
    "fastapi>=0.115.11",
    "langchain>=0.3.20",
    "langchain-openai>=0.3.8",
    "langchain-google-genai>=2.1.9",
    "langgraph>=0.3.5",
    "neo4j>=5.28.1",
    "pydantic-settings>=2.8.1",
    "python-dotenv>=1.0.1",
    "sentry-sdk>=2.22.0",
    "uvicorn>=0.34.0",
    "redis>=5.2.1",
    "langfuse>=2.60.1",
    "motor>=3.3.2",
    "unidecode>=1.4.0",
    "jinja2>=3.1.6",
    "pandas>=2.0.0",
    "tabulate>=0.9.0",
]

[dependency-groups]
dev = [
    "pre-commit>=4.1.0",
    "ruff>=0.9.9",
    "pytest>=8.1.1",
    "pytest-asyncio>=0.23.5",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::DeprecationWarning:pydantic.v1.typing"
]
