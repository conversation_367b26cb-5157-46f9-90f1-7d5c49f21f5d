APP_ENV="production"
APP_HOST="0.0.0.0"
APP_PORT=8080

SENTRY_DSN=""

OPENAI_API_KEY=""

NEO4J_URI="bolt://************:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD=""
# Neo4j Connection Pool Settings (optional)
NEO4J_MAX_CONNECTION_POOL_SIZE=50  # Maximum connections in pool (default: 50)
NEO4J_CONNECTION_TIMEOUT=5  # Connection timeout in seconds (default: 5)
NEO4J_ACQUISITION_TIMEOUT=60  # Timeout for acquiring connection from pool (default: 60)
NEO4J_MAX_RETRY_TIME=30  # Maximum retry time in seconds (default: 30)

USER_PROFILE_SERVICE_URL="https://api.xstaging.navigosgroup.site/jobseekers-info/internal/api/v1/profile"
MARKET_INSIGHTS_SERVICE_URL="https://ai-api.xstaging.navigosgroup.site/market-insights/internal/v1"

REDIS_URI="redis://localhost:6379/0"
REDIS_TTL=86400
# Redis Connection Pool Settings (optional)
REDIS_MAX_CONNECTIONS=50  # Maximum connections in pool (default: 50)

LANGFUSE_SECRET_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_HOST=""

MONGODB_URI=""
MONGODB_DB=""
# MongoDB Connection Pool Settings (optional)
MONGODB_MAX_POOL_SIZE=50  # Maximum connections in pool (default: 50)
MONGODB_MIN_POOL_SIZE=10  # Minimum connections to maintain (default: 10)

# Cache Feature Flags
CACHE_REDIS_ENABLED=true  # Enable/disable Redis caching
CACHE_MONGODB_ENABLED=true  # Enable/disable MongoDB caching
