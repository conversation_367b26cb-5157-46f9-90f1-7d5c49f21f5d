# Career Path Evaluation System

An AI-powered evaluation system that automatically generates test cases, runs them through the career path recommendation graph v4, and evaluates the quality of suggestions using Google Gemini 2.5 Flash.

## Features

- **Automated Test Case Generation**: Uses Gemini AI to create diverse, realistic user profiles
- **Comprehensive Evaluation**: 7 different metrics assess recommendation quality
- **Bias Detection**: Analyzes potential biases across education, experience, and focus areas
- **Detailed Reporting**: Generates both text and JSON reports with insights
- **CLI and API**: Both command-line and REST API interfaces
- **Performance Metrics**: Tracks response times and system performance

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Test Case      │    │  Evaluation      │    │  AI Evaluator   │
│  Generator      │───▶│  Runner          │───▶│  (Gemini)       │
│  (Gemini)       │    │  (Graph v4)      │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Report         │◀───│  Metrics         │◀───│  Evaluation     │
│  Generator      │    │  Calculator      │    │  Results        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Installation & Setup

1. **Install Dependencies**:
   ```bash
   uv sync
   ```

2. **Environment Variables**:
   Add to your `.env` file:
   ```env
   GOOGLE_API_KEY=your_google_api_key_here
   ```

3. **Verify Installation**:
   ```bash
   python run_evaluation_demo.py quick
   ```

## Usage

### CLI Interface

#### Generate Test Cases
```bash
# Generate 20 test cases
uv run python -m app.evaluation.cli generate --count 20 --output test_cases.json

# Generate with specific domains
uv run python -m app.evaluation.cli generate --count 50 --domains "Information Technology,Healthcare" --name "Tech & Health Test Suite"
```

#### Run Evaluation
```bash
# Use existing test suite
uv run python -m app.evaluation.cli evaluate --test-suite test_cases.json --output-dir reports/

# Generate test cases on the fly
uv run python -m app.evaluation.cli evaluate --count 10 --name "Quick Evaluation"
```

#### View Reports
```bash
# Summary view
uv run python -m app.evaluation.cli report reports/evaluation_*.json

# Full details
uv run python -m app.evaluation.cli report reports/evaluation_*.json --format full

# Metrics only
uv run python -m app.evaluation.cli report reports/evaluation_*.json --format metrics
```

### API Interface

#### Start the Server
```bash
uv run python main.py
```

## Evaluation Metrics

The system evaluates career path recommendations using 7 key metrics:

### 1. **Relevance (20% weight)**
How well the suggested career paths align with the user's educational background, experience, and skills.

### 2. **Personalization (15% weight)**
How well the recommendations are tailored to the user's specific characteristics, interests, and career stage.

### 3. **Diversity (10% weight)**
Variety in the suggested career paths - do they cover different industries or specializations?

### 4. **Progression Logic (15% weight)**
Do the career suggestions make logical sense given the user's current level and potential growth paths?

### 5. **Skill Alignment (15% weight)**
How well do the required skills for suggested careers match the user's existing skills and learning capacity?

### 6. **Completeness (15% weight)**
Is sufficient information provided for each career suggestion (descriptions, requirements, growth paths)?

### 7. **Actionability (10% weight)**
How actionable are the recommendations? Are clear next steps or development paths provided?

## Test Case Generation

The system generates diverse test cases across multiple dimensions:

### **Education Levels**
- High School
- Associate Degree
- Bachelor's Degree
- Master's Degree
- Doctorate

### **Experience Levels**
- No Experience
- Entry Level (0-2 years)
- Mid Level (2-5 years)
- Senior Level (5+ years)
- Expert Level

### **Career Stages**
- Fresh Graduate
- Early Career
- Mid Career
- Senior Career
- Career Switcher

### **Focus Areas**
- Information Technology
- Business Development
- Healthcare Services
- Engineering
- Marketing
- Finance
- Education
- And many more...

## Sample Output

```
EVALUATION RESULTS SUMMARY
==========================================
Overall Score (Mean): 78.4/100
Score Range: 65.2 - 89.1
Standard Deviation: 8.7
Success Rate: 100.0%

Metric Breakdown:
  Relevance: 82.1/100
  Personalization: 76.8/100
  Diversity: 74.2/100
  Progression Logic: 79.5/100
  Skill Alignment: 77.9/100
  Completeness: 78.8/100
  Actionability: 79.1/100

Bias Indicators:
  • No significant bias detected
```

## Reports

The system generates comprehensive reports in both text and JSON formats:

### **Text Report Includes**:
- Executive summary
- Detailed metrics breakdown
- Bias analysis
- Top/bottom performing test cases
- Test case diversity analysis
- Actionable recommendations

### **JSON Report Includes**:
- All evaluation data
- Raw scores and explanations
- Test case details
- Aggregate statistics
- Metadata for further analysis

## File Structure

```
app/
├── evaluation/
│   ├── __init__.py
│   ├── models.py          # Pydantic models
│   ├── generator.py       # Test case generation
│   ├── runner.py          # Graph v4 execution
│   ├── evaluator.py       # Gemini evaluation
│   ├── metrics.py         # Statistics calculation
│   ├── report.py          # Report generation
│   ├── cli.py             # Command-line interface
│   ├── demo.py            # Demonstration script
│   └── gemini.py          # Gemini LLM service

tests/
└── evaluation/
    ├── test_models.py     # Model tests
    └── test_metrics.py    # Metrics tests
```

## Configuration

Settings in `app/core/settings.py`:

```python
# Google API configuration
google_api_key: str = ""

# Evaluation system settings
evaluation_batch_size: int = 10
evaluation_timeout: int = 300
evaluation_max_retries: int = 3
evaluation_concurrent_limit: int = 5
```

## Testing

Run the evaluation system tests:

```bash
# Run all evaluation tests
uv run pytest tests/evaluation/

# Run specific test file
uv run pytest tests/evaluation/test_models.py

# Run with coverage
uv run pytest tests/evaluation/ --cov=app.evaluation
```

## Performance Considerations

- **Batch Processing**: Test cases are processed in configurable batches
- **Concurrency Control**: Limited concurrent evaluations to avoid rate limits
- **Retry Logic**: Automatic retries for failed requests
- **Connection Pooling**: Shared HTTP clients for optimal performance
- **Caching**: Results can be cached to avoid redundant evaluations

## Troubleshooting

### Common Issues

1. **Missing API Key**:
   ```
   Error: Missing required environment variables: GOOGLE_API_KEY
   ```
   Solution: Add `GOOGLE_API_KEY=your_key` to `.env` file

2. **Rate Limit Exceeded**:
   ```
   Error: 429 Too Many Requests
   ```
   Solution: Reduce `evaluation_concurrent_limit` in settings

3. **Timeout Errors**:
   ```
   Error: Request timeout
   ```
   Solution: Increase `evaluation_timeout` in settings

4. **Import Errors**:
   ```
   ImportError: No module named 'app.evaluation'
   ```
   Solution: Run `uv sync` to install dependencies

### Debug Mode

Enable detailed logging by setting environment variable:
```bash
export LOG_LEVEL=DEBUG
```

## Contributing

1. **Add New Metrics**: Extend `DetailedEvaluationScore` in `evaluator.py`
2. **Improve Test Generation**: Enhance prompts in `generator.py`
3. **Add New Report Formats**: Extend `EvaluationReporter` in `report.py`
4. **Optimize Performance**: Improve batching and concurrency in `runner.py`

## Future Enhancements

- [ ] Integration with additional LLM providers
- [ ] Real-time evaluation dashboard
- [ ] A/B testing capabilities
- [ ] Historical trend analysis
- [ ] Automated benchmark comparisons
- [ ] Integration with CI/CD pipelines
- [ ] Custom evaluation criteria configuration
- [ ] Multi-language support for test cases
